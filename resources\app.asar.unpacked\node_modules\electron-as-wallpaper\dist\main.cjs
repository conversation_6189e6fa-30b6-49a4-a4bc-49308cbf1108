"use strict";
var __create = Object.create;
var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __getProtoOf = Object.getPrototypeOf;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __export = (target, all) => {
  for (var name in all)
    __defProp(target, name, { get: all[name], enumerable: true });
};
var __copyProps = (to, from, except, desc) => {
  if (from && typeof from === "object" || typeof from === "function") {
    for (let key of __getOwnPropNames(from))
      if (!__hasOwnProp.call(to, key) && key !== except)
        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });
  }
  return to;
};
var __toESM = (mod, isNodeMode, target) => (target = mod != null ? __create(__getProtoOf(mod)) : {}, __copyProps(
  // If the importer is in node compatibility mode or this is not an ESM
  // file that has been converted to a CommonJS file using a Babel-
  // compatible transform (i.e. "__esModule" has not been set), then set
  // "default" to the CommonJS "module.exports" for node compatibility.
  isNodeMode || !mod || !mod.__esModule ? __defProp(target, "default", { value: mod, enumerable: true }) : target,
  mod
));
var __toCommonJS = (mod) => __copyProps(__defProp({}, "__esModule", { value: true }), mod);

// lib/main.ts
var main_exports = {};
__export(main_exports, {
  attach: () => attach,
  detach: () => detach,
  reset: () => reset
});
module.exports = __toCommonJS(main_exports);
var import_bindings = __toESM(require("bindings"), 1);
var import_electron = require("electron");
var neon = (0, import_bindings.default)("neon");
var getNativeWindowHandle = (window) => {
  let buffer = window.getNativeWindowHandle();
  return buffer.readUInt32LE(0);
};
var attachOptions = {
  transparent: false,
  forwardMouseInput: false,
  forwardKeyboardInput: false
};
var attach = (window, options) => {
  if (!(window instanceof import_electron.BrowserWindow)) {
    throw new Error("window must be an instance of Electron.BrowserWindow");
  }
  const nativeWindowHandle = getNativeWindowHandle(window);
  neon.attach(nativeWindowHandle, {
    ...attachOptions,
    ...options
  });
  window.wallpaperState = {
    isAttached: true,
    isTransparent: attachOptions.transparent,
    isForwardMouseInput: attachOptions.forwardMouseInput,
    isForwardKeyboardInput: attachOptions.forwardKeyboardInput
  };
};
var detach = (window) => {
  if (!(window instanceof import_electron.BrowserWindow)) {
    throw new Error("window must be an instance of Electron.BrowserWindow");
  }
  if (!window.wallpaperState?.isAttached) {
    return;
  }
  const nativeWindowHandle = getNativeWindowHandle(window);
  neon.detach(nativeWindowHandle, {
    transparent: window.wallpaperState?.isTransparent,
    forwardMouseInput: window.wallpaperState?.isForwardMouseInput,
    forwardKeyboardInput: window.wallpaperState?.isForwardKeyboardInput
  });
  window.wallpaperState = {
    isAttached: false,
    isTransparent: false,
    isForwardMouseInput: false
  };
};
var reset = () => {
  neon.reset();
};
// Annotate the CommonJS export names for ESM import in node:
0 && (module.exports = {
  attach,
  detach,
  reset
});
