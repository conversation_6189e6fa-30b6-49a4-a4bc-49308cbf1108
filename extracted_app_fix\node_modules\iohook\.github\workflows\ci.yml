name: CI

on:
  workflow_dispatch:
  release:
  push:
  pull_request:
    branches: [master]

jobs:
  ci:
    strategy:
      matrix:
        os: [ubuntu-latest, macos-latest, windows-2019]
        arch: [x86_64]
        include:
          - os: macos-latest
            arch: arm64
    runs-on: ${{ matrix.os }}
    name: ${{ matrix.os }} (${{matrix.arch}})
    steps:
      - name: Checkout Code
        uses: actions/checkout@v4

      - name: Setup Node
        uses: actions/setup-node@v4
        with:
          # Set registry to NPM instead of github packages for publication
          registry-url: 'https://registry.npmjs.org'
          node-version: 14

      - name: Setup macos Dependencies
        if: startsWith(matrix.os, 'macos')
        run: |
          pip install --upgrade setuptools

      - name: Setup Linux Dependencies
        if: startsWith(matrix.os, 'ubuntu')
        run: |
          sudo apt-get update
          sudo apt-get install -y software-properties-common libx11-dev libxtst-dev libxt-dev libx11-xcb-dev libxkbcommon-dev libxkbcommon-x11-dev xorg-dev libxcb-xkb-dev libxkbfile-dev libxinerama-dev

      - name: Install Packages
        run: npm i --ignore-scripts

      # Build only if version tag is not present
      - name: Build
        run: npm run build -- --all --msvs_version=2019
        if: startsWith(github.ref, 'refs/tags/v') != true
        env:
          ARCH: ${{ matrix.arch }}

      # Build and publish if version tag is present
      - name: Build & Publish Prebuilds
        run: npm run build:ci -- --msvs_version=2019
        if: startsWith(github.ref, 'refs/tags/v')
        env:
          ARCH: ${{ matrix.arch }}
          GITHUB_ACCESS_TOKEN: ${{ secrets.GITHUB_TOKEN }}

      - name: Publish NPM Package
        if: startsWith(github.ref, 'refs/tags/v') && startsWith(matrix.os, 'ubuntu')
        run: npm publish --access public
        env:
          NODE_AUTH_TOKEN: ${{ secrets.NODE_AUTH_TOKEN }}
