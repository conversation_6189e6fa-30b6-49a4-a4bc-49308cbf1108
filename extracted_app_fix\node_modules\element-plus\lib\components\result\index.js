'use strict';

Object.defineProperty(exports, '__esModule', { value: true });

var result$1 = require('./src/result.js');
var result = require('./src/result2.js');
var install = require('../../utils/vue/install.js');

const ElResult = install.withInstall(result$1["default"]);

exports.IconComponentMap = result.IconComponentMap;
exports.IconMap = result.IconMap;
exports.resultProps = result.resultProps;
exports.ElResult = ElResult;
exports["default"] = ElResult;
//# sourceMappingURL=index.js.map
