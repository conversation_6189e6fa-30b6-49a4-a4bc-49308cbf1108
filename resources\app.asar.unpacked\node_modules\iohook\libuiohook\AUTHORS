<PERSON> <<EMAIL>>

<PERSON> <<EMAIL>>
	original version of the keycode_to_scancode function in x11/input_helper.c

<PERSON><PERSON> <<EMAIL>>
	contributed patches to include support for media keys and objective-c callbacks in x11/input_hook.c
    contributed hook_create_screen_info property for Windows and Darwin

F<PERSON> <<EMAIL>>
	xfree86_keycode_to_scancode_table lookup in x11/input_helper.c

<PERSON><PERSON><PERSON> <<EMAIL>>
	original version of windows/input_helper.c
	original version of windows/input_helper.h

<PERSON> <<EMAIL>>
	keysym_unicode_table lookup table in x11/input_helper.c
	original version of the unicode_to_keysym function in x11/input_helper.c
	original version of the keysym_to_unicode function in x11/input_helper.c

Alex <<EMAIL>>
	updated objc_msgSend for newer XCode versions in darwin/input_hook.c
	added support for cmake
