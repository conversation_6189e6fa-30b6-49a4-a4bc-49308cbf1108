{"version": 3, "file": "right-table.js", "sources": ["../../../../../../../packages/components/table-v2/src/renderers/right-table.tsx"], "sourcesContent": ["import Table from '../table-grid'\n\nimport type { FunctionalComponent, Ref } from 'vue'\nimport type { TableV2GridProps } from '../grid'\nimport type { TableGridInstance } from '../table-grid'\n\ntype LeftTableProps = TableV2GridProps & {\n  rightTableRef: Ref<TableGridInstance | undefined>\n}\n\nconst LeftTable: FunctionalComponent<LeftTableProps> = (props, { slots }) => {\n  if (!props.columns.length) return\n\n  const { rightTableRef, ...rest } = props\n\n  return (\n    <Table ref={rightTableRef} {...rest}>\n      {slots}\n    </Table>\n  )\n}\n\nexport default LeftTable\n"], "names": ["LeftTable", "slots", "rightTableRef", "rest", "_createVNode", "Table", "_mergeProps"], "mappings": ";;;;;;;;;;;;AAUA,CAAA,KAAMA;AAA2DC,EAAAA,IAAAA,CAAAA,KAAAA,CAAAA,OAAAA,CAAAA,MAAAA;AAAF,IAAc,OAAA;AAC3E,EAAA,MAAU;IAEJ,aAAA;IAAEC,GAAF,IAAA;MAAoBC,KAAAA,CAAAA;AAApB,EAAA,OAANC,eAAA,CAAAC,oBAAA,EAAAC,cAAA,CAAA;AAEA,IAAA,KAAA,EAAA,aAAA;KACcJ,IAAAA,CAAAA,EAAAA,OAAAA,CAAAA,KAAAA,CAAAA,GAAAA,KAAAA,GAAAA;AADd,IAAA,OAAA,EAAA,MAAA,CAEKD,KAFL,CAAA;AAAA,GAAA,CAAA,CAAA;AAAA,CAAA,CAAA;AAKD,iBAVD,SAAA;;;;"}