{"version": 3, "file": "arrow2.js", "sources": ["../../../../../../packages/components/popper/src/arrow.vue"], "sourcesContent": ["<template>\n  <span\n    ref=\"arrowRef\"\n    :class=\"ns.e('arrow')\"\n    :style=\"arrowStyle\"\n    data-popper-arrow\n  />\n</template>\n\n<script lang=\"ts\" setup>\nimport { inject, onBeforeUnmount, watch } from 'vue'\nimport { useNamespace } from '@element-plus/hooks'\nimport { POPPER_CONTENT_INJECTION_KEY } from './constants'\nimport { popperArrowProps } from './arrow'\n\ndefineOptions({\n  name: 'ElPopperArrow',\n  inheritAttrs: false,\n})\n\nconst props = defineProps(popperArrowProps)\n\nconst ns = useNamespace('popper')\nconst { arrowOffset, arrowRef, arrowStyle } = inject(\n  POPPER_CONTENT_INJECTION_KEY,\n  undefined\n)!\n\nwatch(\n  () => props.arrowOffset,\n  (val) => {\n    arrowOffset.value = val\n  }\n)\nonBeforeUnmount(() => {\n  arrowRef.value = undefined\n})\n\ndefineExpose({\n  /**\n   * @description Arrow element\n   */\n  arrowRef,\n})\n</script>\n"], "names": ["useNamespace", "inject", "POPPER_CONTENT_INJECTION_KEY", "watch", "onBeforeUnmount", "_openBlock", "_createElementBlock", "_normalizeClass", "_unref", "_normalizeStyle"], "mappings": ";;;;;;;;;;uCAec,CAAA;AAAA,EACZ,IAAM,EAAA,eAAA;AAAA,EACN,YAAc,EAAA,KAAA;AAChB,CAAA,CAAA,CAAA;;;;;;AAIA,IAAM,MAAA,EAAA,GAAKA,mBAAa,QAAQ,CAAA,CAAA;AAChC,IAAA,MAAM,EAAE,WAAA,EAAa,QAAU,EAAA,UAAA,EAAe,GAAAC,UAAA,CAAAC,sCAAA,EAAA,KAAA,CAAA,CAAA,CAAA;AAAA,IAC5CC,SAAA,CAAA,MAAA,KAAA,CAAA,WAAA,EAAA,CAAA,GAAA,KAAA;AAAA,MACA,WAAA,CAAA,KAAA,GAAA,GAAA,CAAA;AAAA,KACF,CAAA,CAAA;AAEA,IAAAC,mBAAA,CAAA,MAAA;AAAA,MACE,QAAY,CAAA,KAAA,GAAA,KAAA,CAAA,CAAA;AAAA,KAAA,CACZ,CAAC;AACC,IAAA,MAAA,CAAA;AAAoB,MACtB,QAAA;AAAA,KACF,CAAA,CAAA;AACA,IAAA,OAAA,CAAA,IAAA,EAAA,MAAsB,KAAA;AACpB,MAAA,OAAAC,aAAiB,EAAA,EAAAC,sBAAA,CAAA,MAAA,EAAA;AAAA,QAClB,OAAA,EAAA,UAAA;AAED,QAAa,GAAA,EAAA,QAAA;AAAA,QAAA,KAAA,EAAAC,kBAAA,CAAAC,SAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,OAAA,CAAA,CAAA;AAAA,QAAA,KAAA,EAAAC,kBAAA,CAAAD,SAAA,CAAA,UAAA,CAAA,CAAA;AAAA,QAAA,mBAAA,EAAA,EAAA;AAAA,OAIX,EAAA,IAAA,EAAA,CAAA,CAAA,CAAA;AAAA,KACD,CAAA;;;;;;;"}