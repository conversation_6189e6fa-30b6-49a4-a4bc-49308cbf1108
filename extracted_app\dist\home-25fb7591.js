import{d as ge,u as pe,r as c,a as j,o as l,c as u,b as N,w as oe,e as i,f as m,t as r,g as f,O as Ze,_ as fe,F as V,h as U,i as q,j as S,k as Ce,l as p,m as qe,E as O,n as Ge,p as Je,q as We,s as Ke,v as ke,B as re,x as de,y as xe,z as Qe,A as $e,D as et,C as me,G as te,H as Pe,I as tt,J as ae,P as at,T as ot}from"./index-6558a68c.js";import{_ as lt}from"./network-a35c6432.js";import{d as st}from"./downloadList-3dfc77de.js";import{u as nt}from"./updatePrompt-95882057.js";const ut="TimeLimited-5083b4bf.png",it="TimeLimited_en-1fe85d04.png",ct="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAXwAAACyCAMAAAB2rISIAAAAV1BMVEVHcEwYGiIeHh4WHCQYHCIYHCIYICAYHCQaGiIgICAYHCIYHCIWHiIYHCIYHCIYHCIWHCIYHCIYHCIYHCIYHCIYHCIWHCIUHh7Fw8EYHCJucHJCRkqbmZk4tLKiAAAAGHRSTlMAQBBOUGggciYIv/2bu53v28e9s/vNrxgHRxq7AAAI2ElEQVR4AezV6VLjMAwHcLMcBaYMV4FW0vs/5ybN5Tu2k0adWv8PO0tzyb8osvLn5W63f/14P5wki3J4/3jd7+5eVHKedl/cRd9avnZPSfT339yV3ma+72fpHz65i7zdfD5E6Z9/uQu87fw+h+0fZYO9cA6PIfs9d2k1ZO+l//fGXVcdefvn2v/9cFdVS37+nL4X+83yY/e+zJwN8yZ7LWOMXfeRu5ra8jjZPx+4i6kth+cR/5e7lvryO9g/cFdSYx56/E/uQmrMZ2d/z11Hnbk/439zl1Fnvlv7J+4qas1Tg7/jLqLW7Br8L+4ias2XUi/cNdSbF3XHXUK9uZORz5ed2nOXUG/26pW7hHrzqj64S6g3H+qdu4R6864O3CXUm4PirqDmCD5jBJ8xgs8YwWeM4DNG8Bkj+IwRfMYIPmMEnzGCzxjBZ4zgM2ZlfEQEIKLjkQiaIHIv8JqzJj626E4AuNd4tVkLH9EHL/7RrIMfk+9C4u9mDfx5+q7/udd6dVmOn0gv08fNUvx0eml+Jwvx4ZgZ4deyDD/bvtl5uVd8RVmCnzdypPmdLMCHInrRn1KOX2wv+kOK8RfYi36fUvxF9rLtdinEX2gvvX9OGf5ie9FvU4SPWRMGsA3YLwy5l86fEvwMe9KJEUTfSAk+FdG3Mfhl0y3ATx74NHNx9foF+Mu2VJDBMyYfHwrsjckvrT8kGz/bHum8RxB47lB562fjpw6d/nQk932AtH6XXPzMxu9OJwDS9ef2hVqSi59vT6j9v/svOh9IncnEz2t8NHq7nUD9XySt3+ZC+N3ZZOGOWyzltH4/s4gItQ0aIRiMn+I+AYNHhkMYqEy7DJsC4aL4WY0PdmPjMHi0uYNZr3uYYdE2oPgp4WeEH+9VRf0yaEuD2eUswM+bOuQshwZsVyr5gbg+fqQRIu9l/H7PLwLaj6Rp/8vhUyL+IGyXgplzB8I3XxEfIo0ARjvZi5lu2AwmOv+Dl8LHRPvjcDa4NwAbHzOfR1HZEvxYLRDuEeNQ80xqJw9eDB+y8MHTL4MMuVSeULjxV8TXToPwkiH8zs6ltk3fPBhy9tws/NSp0xWAXnxIxx8bv/mcm7+Q9NObCdunvxeNP4AJpx3Qj/rwKYx/DFbXzfx27DSDP8c+Dz+r8QMz38EPzx10TgDf2TgDBxmrwjC+fRsyl9sNe2DHHwogB2TEO6bgk0cVg++oFN8YThQ56n3qdKD5ppCy7LPwMc++xUPn7bmvEWbwIa2sZfjD8Irgg6847RqErN02Ex+yhk6nYt8A3DvR3PMui9+fhP7PEHwLO5mNeCrN+vjaWskkgekTzcKHS+L3Jw2tTJE16zeirfEp0/7cVTT2EkxHj0n4Y3dR9HNehj+eQ97WD+Cj91O/JH6KPbku0K4HkabyrU8o4YEUGaeL8Kdz/LcBf2PR5vjNhj6LbxmhUSX43+KczEz7z+FHHzZOnRHUe5ws/O5H2hL/NDv3IXLBqEfxFxZ+mt9/Eb5Wtnfu9PewtuPuV9wav3lwZPR7xzc0V5A2N2Dma4n5+fiX4OuTyXufAR+M3joT0Gl7/KbKYPsnvbxjBr41t7zf1gr4GqlVzoB/0k/snogs+KF1xbe28JUxfJcf1sQ3bum70Yivt37f+Fz43s03wR59MyvzKlwPH9LxtdbH4SI2fM/i8q9ILh41f8rEj/SEVYBn7kz4U+vTcBEnvtb+RIDx6RHo+vTip8tXw+9PIOwD7p0m/LH10XoLXPh9dTTrDhSS96nFrey5sxg/1g0a/tD6Y+Pz4zdFFbd8Jn6vbGuW44cqQi/+0PrT064AvxNACL0EiNIn7dMm1lr4kNAOOn73f5p+uBZ8CK1zpu3j+OhTXm3shLsCI/jaw64Ff9h87ZXiHH0MH83dhFbGD5dEXnxNH68Mvy8OfOuPJny/8+IIEM9jzTMU0vDRjHWYQI9dkx+ftPquBf9cndaX8yNnpvGTLpjDD71s7+3sj8vAP5mNvzH+7N6Ink+2GJ9mRsIyfPB+eGTdzMQH8+i2+NHdcSivLzSp7Y+zU8cJrosP3puNTzHxT+Zf2+K3T4Oo/9j6mEbvMYvjo++RJfiB+1llgeddkFXgRvjo7RareIos3E3sXu7HA+GiMvH9U2d64378k/HHxhsuzZLRuZhk+7nKdX/yP3URPgTuZk15jGlsjR+ePl0xtEbjjx4IAIjFa7zaZOLjLFzXgsc18W82mfhWS7t00P0o9inJxceZid2BJo/8G5wlGcnFt1wbPAQ0joJzkjR+INn45kRp5zscqUdE6j+G1MbnXj1z8vHRmRttnxMA0dDM0vhpyce39txBv/8SzD+l8aMpwEdn8DTe7RuhM326fe2NX4Jv6Xb6zTvxHhX7SErwbV9dEc2hJPaxFOFbY7+B7Ns+nX78YGpOGf7Jg0yULn+UzbZNIT7mOPuC3Au/hhTiL9WXgd+mFN87eaTv81KOX65PYt9lAf4JCu2513w1WYJfpi/jfswi/BOS2C/IMvzs5pdxr2cpflbzC72ZxfhN85PQl2UF/IYfhL4kq+Cf5mY/Cb0va+E3wz80fghF3p/18NuA9QKIRD6SdfG7ICIAivpsLoEvSYzgM0bwGSP4jBF8xgg+YwSfMYLPGMFnjOAzRvAZI/iMEXzGCD5jBJ8x6sBdQb05qHfuEurNu/rgLqHefKhX7hLqzavac5dQb/Zqx11CvdmpO+4S6s2deuEuod68KPXFXUOt+VJKhj5Xdg3+E3cRteapwVff3FXUme/WXt1zl1Fn7s/46pO7jhrz2dmrB+5CasxDj69+uSupL7+DvXo+cNdSWw7PI7565C6mtjxO9krtuaupK3vdXqk37npqyptpr/79cFdUT37+WfjqT/Q3ys+fbd/0vkyeTfLm9L3sulvF2mvV//bqJAVgEAiiaIlDqwhOjfc/anZZZRCyaGjyTvCpTZ08S6dpx/5ueyAu6TrdVsQT16UD9eoOb+yUjtRpWuwINKRLtRkUsC0ZKrnV/4A/4tpyIZOuVz4AgVpwjv4GxygAAAAASUVORK5CYII=",vt={class:"QRCode_wrap"},rt=["src"],dt={key:1,class:"way_wrap"},mt={key:2,src:ct,class:"steam",alt:""},gt={class:"pour"},pt=ge({__name:"butPopup",emits:["paymentSuccessful"],setup(he,{expose:B,emit:R}){const{t:a}=pe(),_=R,n=c(!1);c(1);const k=c({}),x=c("microsoft"),P=C=>{k.value=C,n.value=!0},Y=()=>{Ze({out_trade_no:k.value.out_trade_no,goodsId:k.value.goodsId}).then(C=>{C.code==200&&(C.data.status==1&&_("paymentSuccessful"),n.value=!1)})};return B({onShowEvent:P}),(C,L)=>{const v=j("el-dialog");return l(),u("div",null,[N(v,{modelValue:n.value,"onUpdate:modelValue":L[0]||(L[0]=H=>n.value=H),width:"320","close-delay":0,top:"31vh",style:{"border-radius":"14px"},"close-on-click-modal":!1},{default:oe(()=>[i("div",vt,[i("div",{class:"guanbi iconfont icon-guanbi",onClick:Y}),x.value!=="steam"?(l(),u("img",{key:0,src:k.value.QR_code_base64,class:"QRCode",alt:""},null,8,rt)):m("",!0),x.value!=="steam"?(l(),u("div",dt,[L[1]||(L[1]=i("div",{class:"icon iconfont icon-weixinzhifu"},null,-1)),i("span",null,r(f(a)("version.text1")),1)])):m("",!0),x.value==="steam"?(l(),u("img",mt)):m("",!0),i("div",gt,r(f(a)("version.text2")),1)])]),_:1},8,["modelValue"])])}}});const be=fe(pt,[["__scopeId","data-v-33e13a18"]]),ft={class:"dialog_wrap"},ht={class:"Carousel"},yt=["src"],_t={class:"info"},It={class:"name"},Tt={class:"description"},wt={class:"money"},kt=ge({__name:"typingDetails",setup(he,{expose:B}){const{t:R}=pe(),a=c(!1),_=c(""),n=c({}),k=c(""),x=y=>{n.value=y,_.value=localStorage.getItem("curPetId"),a.value=!0},P=()=>{a.value=!1},Y=()=>{H()||Ce({payId:1,goodsId:n.value.goodsId}).then(y=>{if(y.code==200){k.value.onShowEvent(y.data);return}})},C=()=>{H()||(p.send("useTyping",n.value.id),P())},L=()=>{P(),p.send("closePet22")},v=()=>{P(),p.send("waySuccess")},H=()=>{let y=!1;return localStorage.getItem("token")||localStorage.setItem("token","fake_token_logged_in"),y};return B({onShowEvent:x,onclose:P}),(y,I)=>{const X=j("el-carousel-item"),G=j("el-carousel"),d=j("el-dialog");return l(),u("div",null,[N(d,{modelValue:a.value,"onUpdate:modelValue":I[3]||(I[3]=o=>a.value=o),width:"1043","close-delay":0,top:"22vh",style:{"border-radius":"14px"},onClose:P,"close-on-click-modal":!1},{default:oe(()=>[N(be,{ref_key:"butPopupRef",ref:k,onPaymentSuccessful:v},null,512),i("div",ft,[i("div",{class:"icon iconfont icon-guanbi",onClick:P}),i("div",ht,[N(G,{"indicator-position":"outside",autoplay:""},{default:oe(()=>[(l(!0),u(V,null,U(n.value.carouselImage,o=>(l(),q(X,{key:o},{default:oe(()=>[i("img",{src:o,style:{width:"100%",height:"100%"},alt:""},null,8,yt)]),_:2},1024))),128))]),_:1})]),i("div",_t,[i("div",It,r(n.value.title),1),i("div",Tt,r(n.value.description),1),i("div",wt,r(n.value.price),1),n.value.showBay==1&&n.value.isTime!=2?(l(),u("div",{key:0,class:"but",onClick:Y},r(f(R)("home.but")),1)):m("",!0),n.value.showBay!=1&&n.value.id!=_.value&&n.value.isTime!=2?(l(),u("div",{key:1,class:"but",onClick:I[0]||(I[0]=S(o=>C(n.value.id),["stop"]))},r(f(R)("home.but1")),1)):m("",!0),n.value.showBay!=1&&n.value.id!=_.value&&n.value.isTime==2?(l(),u("div",{key:2,class:"but",onClick:I[1]||(I[1]=S(o=>C(n.value.id),["stop"]))},r(f(R)("home.but1")),1)):m("",!0),n.value.id==_.value?(l(),u("div",{key:3,class:"but",onClick:I[2]||(I[2]=S(o=>L(),["stop"]))},r(f(R)("home.but2")),1)):m("",!0)])])]),_:1},8,["modelValue"])])}}});const xt=fe(kt,[["__scopeId","data-v-ad5309b1"]]),Pt={class:"app"},Ct={class:"category"},bt=["onClick"],St={class:"span"},Rt={key:0,class:"nav"},Lt={class:"menu"},Ht=["onClick"],Et={class:"span"},zt={class:"Filter"},At={key:0,class:"dropdown",style:{right:"-50px"}},Dt=["onClick"],Mt={class:"Filter"},jt={key:0,class:"dropdown"},Nt=["onClick"],Ot={key:1,class:"nav",style:{"justify-content":"flex-start"}},Bt={class:"search_text"},Yt={key:2,class:"content"},Xt={class:"network_no"},Ft={class:"text"},Vt={key:3,class:"content"},Ut={class:"wrap"},Zt={class:"left"},qt=["onClick"],Gt={class:"image_title"},Jt={class:"text"},Wt={class:"image_wrap"},Kt=["src"],Qt={key:0,src:ut,class:"isTime",alt:""},$t={key:1,src:it,class:"isTime",alt:""},ea={key:2,class:"info_wrap"},ta={class:"info"},aa={class:"name"},oa={class:"price"},la=["onClick"],sa=["onClick"],na=["onClick"],ua={key:3,class:"progress"},ia={key:1},ca={class:"right"},va={key:0,class:"paging"},ra=ge({__name:"home",setup(he){const{shell:B}=require("electron"),R=require("fs"),{t:a}=pe(),_=qe(),n=st(),k=c(""),x=c(""),P=c(""),Y=c(""),C=c(""),L=c(""),v=c({tags:[]}),H=c(""),y=c(!0),I=c(!1),X=c(0),G=c(0),d=c({currentPage:1,selectedId:1,accountId:"",requiredTags:"",fileType:"",searchText:"",imageQuality:"全部"}),o=c([]),D=c(0),b=c(localStorage.getItem("curPetId")),J=c(!1),W=c(!1),le=c(a("home.menu12")),se=c(a("home.menu1")),F=c(0),h=c(0),E=c([]),ne=c(""),K=c(navigator.onLine),Se=c([{name:a("home.nav1"),tagType:""},{name:a("home.nav2"),tagType:"Anime"},{name:a("home.nav3"),tagType:"Game"},{name:a("home.nav4"),tagType:"Cartoon"},{name:a("home.nav5"),tagType:"sCENERY"},{name:a("home.nav6"),tagType:"tECH"},{name:a("home.nav7"),tagType:"aNIMAL"},{name:a("home.nav8"),tagType:"MMD"},{name:a("home.nav9"),tagType:"Pixel"},{name:a("home.nav10"),tagType:"Film"},{name:a("home.nav11"),tagType:"Cute"},{name:a("home.nav12"),tagType:"General"}]),Re=c([{name:a("home.nav1"),tagType:""},{name:a("home.menu4"),tagType:"video"},{name:a("home.menu5"),tagType:"hudong"},{name:a("home.menu9"),tagType:"clock"},{name:a("home.menu10"),tagType:"typing"},{name:a("home.menu11"),tagType:"music"},{name:a("home.menu6"),tagType:"mouse"}]),Le=c([{name:a("home.menu1"),RuleType:1},{name:a("home.menu7"),RuleType:2},{name:a("home.menu8"),RuleType:3}]),He=c([{name:a("home.menu12"),RuleType:"全部"},{name:a("home.menu13"),RuleType:"4K"},{name:a("home.menu14"),RuleType:"2K"},{name:a("home.menu15"),RuleType:"1080P"},{name:a("home.menu16"),RuleType:"带鱼屏"}]),Ee=t=>{se.value=t.name,d.value.selectedId=t.RuleType,J.value=!1,T()},ze=t=>{le.value=t.name,d.value.imageQuality=t.RuleType,W.value=!1,T()},Ae=()=>{y.value=!0,_.searchText="",d.value.currentPage=1,d.value.accountId="",d.value.selectedId=1,d.value.fileType="",d.value.searchText="",d.value.requiredTags="",F.value=0,T()},ye=()=>ne.value.includes("zh"),_e=(t,e)=>{if(localStorage.removeItem("currentPage"),localStorage.removeItem("tagType"),localStorage.setItem("category",t.tagType),localStorage.setItem("curMenuIndex","0"),localStorage.setItem("curCatIndex",e),h.value=e,F.value=0,d.value.currentPage=1,d.value.requiredTags="",t.tagType?d.value.fileType=t.tagType:d.value.fileType="",d.value.fileType=="typing"){ot().then(g=>{if(g.code==200&&g.data.status!=0){C.value.onShowEvent(g.data.updateUrl);return}T()});return}T()},De=()=>{_e({tagType:""},0)},Me=(t,e)=>{localStorage.removeItem("currentPage"),localStorage.setItem("tagType",t.tagType),localStorage.setItem("curMenuIndex",e),F.value=e,d.value.currentPage=1,t.tagType?d.value.requiredTags=t.tagType:d.value.requiredTags="",T()},Q=()=>{localStorage.removeItem("curPetId"),b.value=localStorage.getItem("curPetId"),localStorage.setItem("closePet","false"),p.send("closePet",!1)},Ie=async()=>{let t=!1;const e=await de({goodsId:v.value.goodsId});return(e==null?void 0:e.code)!==200&&(p.send("againLogin"),t=!0),t},Z=async t=>{await Ie()||re({id:t}).then(e=>{e.code==200&&(v.value=e.data,de({goodsId:e.data.goodsId}).then(g=>{x.value&&x.value.onShowEvent(v.value),xe().then(async A=>{if(!A.data.path){$();return}const ce=A.data.path.lastIndexOf("\\"),ve=A.data.path.substring(0,ce);R.access(ve,R.constants.F_OK,s=>{if(s){console.log("找不到",s),$();return}localStorage.setItem("closePet","true"),localStorage.setItem("curPetId",v.value.id),b.value=localStorage.getItem("curPetId"),A.code==200&&p.send("restartTyping",A.data)})})}))})},je=(t,e)=>{v.value=t,ee(t,!0)},Te=async t=>{await Ie()||re({id:t.id}).then(e=>{e.code==200&&(v.value=e.data,x.value.onShowEvent(v.value),Ce({payId:1,goodsId:e.data.goodsId}).then(g=>{g.code==200&&Y.value.onShowEvent(g.data)}))})},$=()=>{ee(v.value);let t=-1;E.value.forEach((e,g)=>{e.id===v.value.id&&(t=g)}),E.value[t].showBay=2,!o.value.some(e=>e.id===v.value.id)&&(o.value.push(v.value),n.setList(o.value),!(o.value.length>1)&&z())},we=t=>{h.value===1&&(H.value=t,y.value=!1,d.value.accountId=t,d.value.currentPage=1,F.value=0,Ue())},Ne=t=>o.value.some(e=>e.id===t);let ue=null;const Oe=()=>{if(ue){O.error(a("home.text2"));return}ue=setTimeout(()=>{ue=null},1e3),Qe(h.value,v.value).then(t=>{if(t){if(o.value.some(e=>e.id===v.value.id))return;if(n.getLists().length!==0){O.error(a("System.text1"));return}$e(v.value).then(e=>{if(e){O.error(a("home.text1"));return}et({id:v.value.id}).then(g=>{if(g.code==200){if(v.value.path=g.data.path,o.value.push(v.value),n.setList(o.value),o.value.length>1)return;z();return}O.error(g.message)})})}else{let e=6,g="rainmeter";h.value==6&&(e=8,g="mouse"),L.value.onShowEvent(e,g)}})},ie=c(""),z=()=>{const t=JSON.stringify({url:o.value[0].path,gifUrl:o.value[0].previewUrl,title:o.value[0].title,id:o.value[0].id,accountId:o.value[0].accountId,description:o.value[0].description,resolution:o.value[0].resolution,size:o.value[0].size,tags:o.value[0].tags,userName:o.value[0].userName,fileType:o.value[0].fileType,local:!1,musicFileName:o.value[0].musicFileName});ie.value=setTimeout(()=>{D.value=0,n.delList(),o.value=n.list,o.value.length>0&&z()},5e3),o.value[0].fileType=="video"?p.invoke("DownloadInfo",t).then(e=>{me(_,e),te({id:o.value[0].id}),D.value=0,n.delList(),o.value=n.list,o.value.length>0&&z()}).catch(e=>{O.error(a("home.text1")),n.delList(),o.value=n.list,o.value.length>0&&z()}):o.value[0].fileType=="hudong"||o.value[0].fileType=="clock"?p.invoke("DownloadASARlocal",t).then(e=>{me(_,e),te({id:e.id}).then(g=>{D.value=0,n.delList(),o.value=n.list,o.value.length>0&&z()})}):o.value[0].fileType=="typing"?p.invoke("DownloadTyping",t).then(e=>{de({goodsId:v.value.goodsId,path:e}).then(g=>{localStorage.setItem("closePet","true"),localStorage.setItem("curPetId",v.value.id),b.value=localStorage.getItem("curPetId"),xe().then(A=>{p.send("restartTyping",A.data)}),o.value.length!=0&&(te({id:o.value[0].id}),D.value=0,n.delList(),o.value=n.list,o.value.length>0&&z())})}):(o.value[0].fileType=="music"||o.value[0].fileType=="mouse")&&p.invoke("DownloadMusicScore",t).then(e=>{me(_,e),te({id:e.id}).then(g=>{D.value=0,n.delList(),o.value=n.list,o.value.length>0&&z()})})},Be=t=>{d.value.currentPage=t,localStorage.setItem("currentPage",t),T()},Ye=c(""),Xe=t=>{B.openExternal(t.url)},Fe=c(""),Ve=t=>{B.openExternal(t.url)},T=()=>{K.value&&(I.value=!1,E.value=[],Pe(d.value).then(t=>{X.value=t.data.totalCount,G.value=t.data.pageSize,E.value=t.data.currentPageRecords,I.value=!0,tt(()=>{if(h.value==4)return;const e=Ye.value;if(!e)return;e.addEventListener("new-window",Xe),Fe.value.addEventListener("new-window",Ve)}),ee(E.value[0])}))},ee=(t,e)=>{t&&re({id:t.id}).then(g=>{if(g){if(v.value=g.data,e&&h.value==4&&P.value.onShowEvent(g.data),h.value==4){x.value.onShowEvent(v.value);return}k.value&&k.value.onShowEvent(v.value)}})},Ue=()=>{localStorage.removeItem("currentPage"),I.value=!1,E.value=[],Pe(d.value).then(t=>{X.value=t.data.totalCount,E.value=t.data.currentPageRecords,I.value=!0,ee(E.value[0])})};return p.on("loginSuccessEvent",(t,e)=>{T()}),p.on("useTyping",(t,e)=>{Z(e)}),p.on("onClosePet",(t,e)=>{Q()}),p.on("UploadSuccessful",(t,e)=>{T()}),p.on("onWaySuccess",(t,e)=>{$()}),p.on("DownloadFailure",(t,e)=>{O.error(a("home.text1")),n.delList(),o.value=n.list,o.value.length>0&&z()}),p.on("DownloadProgress",(t,e)=>{clearTimeout(ie.value),ie.value=null,D.value=e.toFixed(2)}),p.on("RefreshList",(t,e)=>{T()}),p.on("JudgingTypingFollow",(t,e)=>{Ge({uniqueId:e}).then(g=>{g.code!=200&&(O.error(g.message),Q())})}),Je(()=>_.searchText,(t,e)=>{localStorage.removeItem("currentPage"),d.value.currentPage=1,d.value.searchText=t,t?y.value=!1:y.value=!0,H.value=d.value.searchText,T()}),We(()=>{D.value=n.progress,o.value=n.list}),Ke(()=>{localStorage.getItem("setLang")?ne.value=localStorage.getItem("setLang"):ne.value=p.sendSync("Language"),d.value.currentPage=localStorage.getItem("currentPage")||1,d.value.requiredTags=localStorage.getItem("tagType"),d.value.fileType=localStorage.getItem("category"),F.value=Number(localStorage.getItem("curMenuIndex"))||0,h.value=Number(localStorage.getItem("curCatIndex"))||0,_.searchText?(d.value.searchText=_.searchText,y.value=!1,H.value=d.value.searchText,T()):(T(),o.value=n.list)}),(t,e)=>{const g=j("el-progress"),A=j("WallpaperDetails"),ce=j("typingInfo"),ve=j("paging");return l(),u("div",Pt,[i("div",Ct,[(l(!0),u(V,null,U(Re.value,(s,w)=>(l(),u("div",{class:ae(["category_item",h.value==w?"cta_active":""]),key:w,onClick:M=>_e(s,w)},[i("div",St,r(s.name),1),e[8]||(e[8]=i("div",{class:"category_bg"},null,-1))],10,bt))),128))]),y.value?(l(),u("div",Rt,[i("div",Lt,[(l(!0),u(V,null,U(Se.value,(s,w)=>(l(),u("div",{class:ae(["ment_item",F.value==w?"active":""]),key:w,onClick:M=>Me(s,w)},[i("div",Et,r(s.name),1)],10,Ht))),128))]),i("div",zt,[i("div",{class:"show",onClick:e[0]||(e[0]=s=>W.value=!W.value)},[ke(r(le.value)+" ",1),e[9]||(e[9]=i("div",{class:"iconfont icon-xiala icon"},null,-1))]),W.value?(l(),u("div",At,[(l(!0),u(V,null,U(He.value,(s,w)=>(l(),u("div",{class:ae(["dropdown-item",le.value==s.name?"dropdown-item-avtive":""]),key:w,onClick:M=>ze(s)},r(s.name),11,Dt))),128))])):m("",!0)]),i("div",Mt,[i("div",{class:"show",onClick:e[1]||(e[1]=s=>J.value=!J.value)},[ke(r(se.value)+" ",1),e[10]||(e[10]=i("div",{class:"iconfont icon-xiala icon"},null,-1))]),J.value?(l(),u("div",jt,[(l(!0),u(V,null,U(Le.value,(s,w)=>(l(),u("div",{class:ae(["dropdown-item",se.value==s.name?"dropdown-item-avtive":""]),key:w,onClick:M=>Ee(s)},r(s.name),11,Nt))),128))])):m("",!0)])])):m("",!0),y.value?m("",!0):(l(),u("div",Ot,[i("div",{class:"searchIcon iconfont icon-fanhui",onClick:Ae}),i("div",Bt,r(f(a)("home.Search"))+" "+r(H.value)+" "+r(f(a)("home.Search1"))+" "+r(X.value)+" "+r(f(a)("home.Search2")),1)])),K.value?m("",!0):(l(),u("div",Yt,[i("div",Xt,[e[11]||(e[11]=i("img",{src:lt,class:"network",alt:""},null,-1)),i("div",Ft,[i("p",null,r(f(a)("home.prompt1")),1),i("p",null,r(f(a)("home.prompt2")),1)]),i("div",{class:"networkIcon iconfont icon-shuaxin",onClick:e[2]||(e[2]=s=>T())})])])),K.value?(l(),u("div",Vt,[i("div",Ut,[i("div",Zt,[(l(!0),u(V,null,U(E.value,(s,w)=>(l(),u("div",{class:"item",key:w,onClick:M=>je(s)},[i("div",Gt,[i("div",Jt,r(s.title),1)]),i("div",Wt,[i("img",{src:s.previewUrl,class:"image",alt:""},null,8,Kt)]),s.isTime==2&&ye()?(l(),u("img",Qt)):m("",!0),s.isTime==2&&!ye()?(l(),u("img",$t)):m("",!0),h.value==4?(l(),u("div",ea,[i("div",ta,[i("div",aa,r(s.title),1),i("div",oa,r(s.price),1)]),s.showBay==1&&s.isTime!=2?(l(),u("div",{key:0,class:"buy",onClick:S(M=>Te(s),["stop"])},r(f(a)("home.but")),9,la)):m("",!0),h.value==4&&s.showBay!=1&&s.id!=b.value&&s.isTime!=2?(l(),u("div",{key:1,class:"buy",onClick:S(M=>Z(s.id,s.isTime),["stop"])},r(f(a)("home.but1")),9,sa)):m("",!0),h.value==4&&s.id!=b.value&&s.isTime==2&&s.id!=b.value?(l(),u("div",{key:2,class:"buy",onClick:S(M=>Z(s.id),["stop"])},r(f(a)("home.but1")),9,na)):m("",!0),h.value==4&&s.id==b.value?(l(),u("div",{key:3,class:"buy",onClick:e[3]||(e[3]=S(M=>Q(),["stop"]))},r(f(a)("home.but2")),1)):m("",!0)])):m("",!0),Ne(s.id)?(l(),u("div",ua,[o.value[0].id==s.id?(l(),q(g,{key:0,type:"dashboard",percentage:parseFloat(D.value)},null,8,["percentage"])):(l(),u("div",ia,r(f(a)("home.dowText")),1))])):m("",!0)],8,qt))),128))]),i("div",ca,[h.value!=4?(l(),q(A,{key:0,ref_key:"WallpaperDetailsRef",ref:k,onAccountId:we},null,512)):m("",!0),h.value==4?(l(),q(ce,{key:1,ref_key:"typingInfoRef",ref:x,onAccountId:we},null,512)):m("",!0),h.value!=4?(l(),u("div",{key:2,class:"but",onClick:Oe},r(f(a)("General.text6")),1)):m("",!0),h.value==4&&v.value.showBay==1&&v.value.isTime!=2?(l(),u("div",{key:3,class:"but",onClick:e[4]||(e[4]=s=>Te(v.value))},r(f(a)("home.but")),1)):m("",!0),h.value==4&&v.value.showBay!=1&&v.value.id!=b.value&&v.value.isTime!=2?(l(),u("div",{key:4,class:"but",onClick:e[5]||(e[5]=S(s=>Z(v.value.id),["stop"]))},r(f(a)("home.but1")),1)):m("",!0),h.value==4&&v.value.isTime==2&&v.value.id!=b.value?(l(),u("div",{key:5,class:"but",onClick:e[6]||(e[6]=S(s=>Z(v.value.id),["stop"]))},r(f(a)("home.but1")),1)):m("",!0),h.value==4&&v.value.id==b.value?(l(),u("div",{key:6,class:"but",onClick:e[7]||(e[7]=S(s=>Q(),["stop"]))},r(f(a)("home.but2")),1)):m("",!0)])]),K.value?(l(),u("div",va,[I.value?(l(),q(ve,{key:0,onOnChangePage:Be,pageSize:G.value,total:X.value,pagingRight:!1,currentPage:d.value.currentPage},null,8,["pageSize","total","currentPage"])):m("",!0)])):m("",!0)])):m("",!0),N(xt,{ref_key:"typingDetailsRef",ref:P},null,512),N(be,{ref_key:"butPopupRef",ref:Y,onPaymentSuccessful:$},null,512),N(nt,{ref_key:"updatePromptRef",ref:C,onClose:De},null,512),N(at,{ref_key:"PMTRef",ref:L},null,512)])}}});const fa=fe(ra,[["__scopeId","data-v-b5fc5f75"]]);export{fa as default};
