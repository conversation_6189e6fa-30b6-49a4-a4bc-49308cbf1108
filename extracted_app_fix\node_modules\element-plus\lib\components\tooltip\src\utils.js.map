{"version": 3, "file": "utils.js", "sources": ["../../../../../../packages/components/tooltip/src/utils.ts"], "sourcesContent": ["import { unref } from 'vue'\nimport { isArray } from '@element-plus/utils'\nimport type { Arrayable } from '@element-plus/utils'\nimport type { Ref } from 'vue'\nimport type { TooltipTriggerType } from './trigger'\n\nexport const isTriggerType = (\n  trigger: Arrayable<TooltipTriggerType>,\n  type: TooltipTriggerType\n) => {\n  if (isArray(trigger)) {\n    return trigger.includes(type)\n  }\n  return trigger === type\n}\n\nexport const whenTrigger = (\n  trigger: Ref<Arrayable<TooltipTriggerType>>,\n  type: TooltipTriggerType,\n  handler: (e: Event) => void\n) => {\n  return (e: Event) => {\n    isTriggerType(unref(trigger), type) && handler(e)\n  }\n}\n"], "names": ["isArray", "unref"], "mappings": ";;;;;;;AAEY,MAAC,aAAa,GAAG,CAAC,OAAO,EAAE,IAAI,KAAK;AAChD,EAAE,IAAIA,cAAO,CAAC,OAAO,CAAC,EAAE;AACxB,IAAI,OAAO,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;AAClC,GAAG;AACH,EAAE,OAAO,OAAO,KAAK,IAAI,CAAC;AAC1B,EAAE;AACU,MAAC,WAAW,GAAG,CAAC,OAAO,EAAE,IAAI,EAAE,OAAO,KAAK;AACvD,EAAE,OAAO,CAAC,CAAC,KAAK;AAChB,IAAI,aAAa,CAACC,SAAK,CAAC,OAAO,CAAC,EAAE,IAAI,CAAC,IAAI,OAAO,CAAC,CAAC,CAAC,CAAC;AACtD,GAAG,CAAC;AACJ;;;;;"}