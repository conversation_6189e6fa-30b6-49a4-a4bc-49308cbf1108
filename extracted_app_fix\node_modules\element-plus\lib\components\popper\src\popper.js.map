{"version": 3, "file": "popper.js", "sources": ["../../../../../../packages/components/popper/src/popper.ts"], "sourcesContent": ["/* eslint-disable @typescript-eslint/no-unused-vars */\n\nimport { buildProps } from '@element-plus/utils'\n\nimport type { ExtractPropTypes } from 'vue'\nimport type Popper from './popper.vue'\n\nconst effects = ['light', 'dark'] as const\nconst triggers = ['click', 'contextmenu', 'hover', 'focus'] as const\n\nexport const Effect = {\n  LIGHT: 'light',\n  DARK: 'dark',\n} as const\n\nexport const roleTypes = [\n  'dialog',\n  'grid',\n  'group',\n  'listbox',\n  'menu',\n  'navigation',\n  'tooltip',\n  'tree',\n] as const\n\nexport type PopperEffect =\n  | typeof effects[number]\n  | (string & NonNullable<unknown>)\nexport type PopperTrigger = typeof triggers[number]\n\nexport const popperProps = buildProps({\n  role: {\n    type: String,\n    values: roleTypes,\n    default: 'tooltip',\n  },\n} as const)\n\nexport type PopperProps = ExtractPropTypes<typeof popperProps>\n\nexport type PopperInstance = InstanceType<typeof Popper> & unknown\n\n/** @deprecated use `popperProps` instead, and it will be deprecated in the next major version */\nexport const usePopperProps = popperProps\n\n/** @deprecated use `PopperProps` instead, and it will be deprecated in the next major version */\nexport type UsePopperProps = PopperProps\n"], "names": ["buildProps"], "mappings": ";;;;;;AAGY,MAAC,MAAM,GAAG;AACtB,EAAE,KAAK,EAAE,OAAO;AAChB,EAAE,IAAI,EAAE,MAAM;AACd,EAAE;AACU,MAAC,SAAS,GAAG;AACzB,EAAE,QAAQ;AACV,EAAE,MAAM;AACR,EAAE,OAAO;AACT,EAAE,SAAS;AACX,EAAE,MAAM;AACR,EAAE,YAAY;AACd,EAAE,SAAS;AACX,EAAE,MAAM;AACR,EAAE;AACU,MAAC,WAAW,GAAGA,kBAAU,CAAC;AACtC,EAAE,IAAI,EAAE;AACR,IAAI,IAAI,EAAE,MAAM;AAChB,IAAI,MAAM,EAAE,SAAS;AACrB,IAAI,OAAO,EAAE,SAAS;AACtB,GAAG;AACH,CAAC,EAAE;AACS,MAAC,cAAc,GAAG;;;;;;;"}