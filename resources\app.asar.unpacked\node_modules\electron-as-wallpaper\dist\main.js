// lib/main.ts
import bindings from "bindings";
import { BrowserWindow } from "electron";
var neon = bindings("neon");
var getNativeWindowHandle = (window) => {
  let buffer = window.getNativeWindowHandle();
  return buffer.readUInt32LE(0);
};
var attachOptions = {
  transparent: false,
  forwardMouseInput: false,
  forwardKeyboardInput: false
};
var attach = (window, options) => {
  if (!(window instanceof BrowserWindow)) {
    throw new Error("window must be an instance of Electron.BrowserWindow");
  }
  const nativeWindowHandle = getNativeWindowHandle(window);
  neon.attach(nativeWindowHandle, {
    ...attachOptions,
    ...options
  });
  window.wallpaperState = {
    isAttached: true,
    isTransparent: attachOptions.transparent,
    isForwardMouseInput: attachOptions.forwardMouseInput,
    isForwardKeyboardInput: attachOptions.forwardKeyboardInput
  };
};
var detach = (window) => {
  if (!(window instanceof BrowserWindow)) {
    throw new Error("window must be an instance of Electron.BrowserWindow");
  }
  if (!window.wallpaperState?.isAttached) {
    return;
  }
  const nativeWindowHandle = getNativeWindowHandle(window);
  neon.detach(nativeWindowHandle, {
    transparent: window.wallpaperState?.isTransparent,
    forwardMouseInput: window.wallpaperState?.isForwardMouseInput,
    forwardKeyboardInput: window.wallpaperState?.isForwardKeyboardInput
  });
  window.wallpaperState = {
    isAttached: false,
    isTransparent: false,
    isForwardMouseInput: false
  };
};
var reset = () => {
  neon.reset();
};
export {
  attach,
  detach,
  reset
};
