<!DOCTYPE html>
<html lang="zh">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>录制视频</title>
</head>
<style>
    * {
        margin: 0;
        padding: 0;
    }

    body {
        cursor: none;
    }
</style>

<body>
    <div style="width: 100vw;height: 100vh; position: relative;">
        <video style="object-fit: cover; width: 100%;height: 100vh;position: absolute; 
        overflow: hidden;z-index: -1;" autoplay loop class="video">
            您的浏览器不支持视频标签。
        </video>
    </div>

    <script>
        const videoPlayer = document.querySelector('.video');

        document.addEventListener('DOMContentLoaded', () => {
            // 获取 URL 参数（此处代码未使用，但保留以备将来需要）
            const urlParams = new URLSearchParams(window.location.search);
            // 视频源路径（此处为硬编码路径，也可以从 URL 参数中获取）
            const IndexSrc = JSON.parse(urlParams.get('src'));
            
            // 设置视频源并加载视频
            if (videoPlayer && IndexSrc) {
                videoPlayer.src = IndexSrc.path;//给video赋值

            } else {
                console.error('未找到视频源');
            }
        });
        videoPlayer.onloadedmetadata = function () {
            window.electronAPI.sendMessageToMain({
                type: 1,
                name: 'index'
            });//从主程序获取显示屏幕
            setTimeout(() => {
                if (mediaRecorder) {
                    mediaRecorder.stop();
                    document.body.style.cursor = '';
                }
            }, 8000);
        };


        // 使用暴露的API来监听来自主进程的消息
        let mediaRecorder;
        let recordedChunks = [];
        window.electronAPI.on('receiveMonitor', async (arg) => {
            console.log(arg);
            const sources = arg
            const source = sources.find(s => s.name === 'Entire Screen' || s.name.startsWith('Screen '));
            const stream = await navigator.mediaDevices.getUserMedia({
                video: {
                    mandatory: {
                        chromeMediaSource: 'desktop',
                        chromeMediaSourceId: source.id,
                        minWidth: 1920,
                        maxWidth: 1920,
                        minHeight: 1080,
                        maxHeight: 1080,
                    },
                },
            });
            mediaRecorder = new MediaRecorder(stream);
            mediaRecorder.ondataavailable = event => {
                if (event.data.size > 0) {
                    recordedChunks.push(event.data);
                }
            };
            mediaRecorder.onstop = async () => {
                const blob = new Blob(recordedChunks, { type: 'video/webm' });
                window.electronAPI.sendMessageToMain1(blob);

                setTimeout(() => {
                    window.electronAPI.sendMessageToMain({
                        type: 0
                    });
                }, 2000);
            };
            mediaRecorder.start();
        });


    </script>
</body>

</html>