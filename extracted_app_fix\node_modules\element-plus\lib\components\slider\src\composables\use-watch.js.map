{"version": 3, "file": "use-watch.js", "sources": ["../../../../../../../packages/components/slider/src/composables/use-watch.ts"], "sourcesContent": ["import { watch } from 'vue'\nimport { INPUT_EVENT, UPDATE_MODEL_EVENT } from '@element-plus/constants'\nimport { debugWarn, isArray, isNumber, throwError } from '@element-plus/utils'\nimport type { ComputedRef, SetupContext } from 'vue'\nimport type { Arrayable } from '@element-plus/utils'\nimport type { FormItemContext } from '@element-plus/components/form'\nimport type { SliderEmits, SliderInitData, SliderProps } from '../slider'\n\nexport const useWatch = (\n  props: SliderProps,\n  initData: SliderInitData,\n  minValue: ComputedRef<number>,\n  maxValue: ComputedRef<number>,\n  emit: SetupContext<SliderEmits>['emit'],\n  elFormItem: FormItemContext\n) => {\n  const _emit = (val: Arrayable<number>) => {\n    emit(UPDATE_MODEL_EVENT, val)\n    emit(INPUT_EVENT, val)\n  }\n\n  const valueChanged = () => {\n    if (props.range) {\n      return ![minValue.value, maxValue.value].every(\n        (item, index) => item === (initData.oldValue as number[])[index]\n      )\n    } else {\n      return props.modelValue !== initData.oldValue\n    }\n  }\n\n  const setValues = () => {\n    if (props.min > props.max) {\n      throwError('Slider', 'min should not be greater than max.')\n    }\n    const val = props.modelValue\n    if (props.range && isArray(val)) {\n      if (val[1] < props.min) {\n        _emit([props.min, props.min])\n      } else if (val[0] > props.max) {\n        _emit([props.max, props.max])\n      } else if (val[0] < props.min) {\n        _emit([props.min, val[1]])\n      } else if (val[1] > props.max) {\n        _emit([val[0], props.max])\n      } else {\n        initData.firstValue = val[0]\n        initData.secondValue = val[1]\n        if (valueChanged()) {\n          if (props.validateEvent) {\n            elFormItem?.validate?.('change').catch((err) => debugWarn(err))\n          }\n          initData.oldValue = val.slice()\n        }\n      }\n    } else if (!props.range && isNumber(val) && !Number.isNaN(val)) {\n      if (val < props.min) {\n        _emit(props.min)\n      } else if (val > props.max) {\n        _emit(props.max)\n      } else {\n        initData.firstValue = val\n        if (valueChanged()) {\n          if (props.validateEvent) {\n            elFormItem?.validate?.('change').catch((err) => debugWarn(err))\n          }\n          initData.oldValue = val\n        }\n      }\n    }\n  }\n\n  setValues()\n\n  watch(\n    () => initData.dragging,\n    (val) => {\n      if (!val) {\n        setValues()\n      }\n    }\n  )\n\n  watch(\n    () => props.modelValue,\n    (val, oldVal) => {\n      if (\n        initData.dragging ||\n        (isArray(val) &&\n          isArray(oldVal) &&\n          val.every((item, index) => item === oldVal[index]) &&\n          initData.firstValue === val[0] &&\n          initData.secondValue === val[1])\n      ) {\n        return\n      }\n      setValues()\n    },\n    {\n      deep: true,\n    }\n  )\n\n  watch(\n    () => [props.min, props.max],\n    () => {\n      setValues()\n    }\n  )\n}\n"], "names": ["UPDATE_MODEL_EVENT", "INPUT_EVENT", "throwError", "isArray", "debugWarn", "isNumber", "watch"], "mappings": ";;;;;;;;;;AAGY,MAAC,QAAQ,GAAG,CAAC,KAAK,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,IAAI,EAAE,UAAU,KAAK;AACnF,EAAE,MAAM,KAAK,GAAG,CAAC,GAAG,KAAK;AACzB,IAAI,IAAI,CAACA,wBAAkB,EAAE,GAAG,CAAC,CAAC;AAClC,IAAI,IAAI,CAACC,iBAAW,EAAE,GAAG,CAAC,CAAC;AAC3B,GAAG,CAAC;AACJ,EAAE,MAAM,YAAY,GAAG,MAAM;AAC7B,IAAI,IAAI,KAAK,CAAC,KAAK,EAAE;AACrB,MAAM,OAAO,CAAC,CAAC,QAAQ,CAAC,KAAK,EAAE,QAAQ,CAAC,KAAK,CAAC,CAAC,KAAK,CAAC,CAAC,IAAI,EAAE,KAAK,KAAK,IAAI,KAAK,QAAQ,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC;AACzG,KAAK,MAAM;AACX,MAAM,OAAO,KAAK,CAAC,UAAU,KAAK,QAAQ,CAAC,QAAQ,CAAC;AACpD,KAAK;AACL,GAAG,CAAC;AACJ,EAAE,MAAM,SAAS,GAAG,MAAM;AAC1B,IAAI,IAAI,EAAE,EAAE,EAAE,CAAC;AACf,IAAI,IAAI,KAAK,CAAC,GAAG,GAAG,KAAK,CAAC,GAAG,EAAE;AAC/B,MAAMC,gBAAU,CAAC,QAAQ,EAAE,qCAAqC,CAAC,CAAC;AAClE,KAAK;AACL,IAAI,MAAM,GAAG,GAAG,KAAK,CAAC,UAAU,CAAC;AACjC,IAAI,IAAI,KAAK,CAAC,KAAK,IAAIC,cAAO,CAAC,GAAG,CAAC,EAAE;AACrC,MAAM,IAAI,GAAG,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,GAAG,EAAE;AAC9B,QAAQ,KAAK,CAAC,CAAC,KAAK,CAAC,GAAG,EAAE,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC;AACtC,OAAO,MAAM,IAAI,GAAG,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,GAAG,EAAE;AACrC,QAAQ,KAAK,CAAC,CAAC,KAAK,CAAC,GAAG,EAAE,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC;AACtC,OAAO,MAAM,IAAI,GAAG,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,GAAG,EAAE;AACrC,QAAQ,KAAK,CAAC,CAAC,KAAK,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACnC,OAAO,MAAM,IAAI,GAAG,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,GAAG,EAAE;AACrC,QAAQ,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC;AACnC,OAAO,MAAM;AACb,QAAQ,QAAQ,CAAC,UAAU,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC;AACrC,QAAQ,QAAQ,CAAC,WAAW,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC;AACtC,QAAQ,IAAI,YAAY,EAAE,EAAE;AAC5B,UAAU,IAAI,KAAK,CAAC,aAAa,EAAE;AACnC,YAAY,CAAC,EAAE,GAAG,UAAU,IAAI,IAAI,GAAG,KAAK,CAAC,GAAG,UAAU,CAAC,QAAQ,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,UAAU,EAAE,QAAQ,CAAC,CAAC,KAAK,CAAC,CAAC,GAAG,KAAKC,eAAS,CAAC,GAAG,CAAC,CAAC,CAAC;AACrJ,WAAW;AACX,UAAU,QAAQ,CAAC,QAAQ,GAAG,GAAG,CAAC,KAAK,EAAE,CAAC;AAC1C,SAAS;AACT,OAAO;AACP,KAAK,MAAM,IAAI,CAAC,KAAK,CAAC,KAAK,IAAIC,cAAQ,CAAC,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE;AACpE,MAAM,IAAI,GAAG,GAAG,KAAK,CAAC,GAAG,EAAE;AAC3B,QAAQ,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;AACzB,OAAO,MAAM,IAAI,GAAG,GAAG,KAAK,CAAC,GAAG,EAAE;AAClC,QAAQ,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;AACzB,OAAO,MAAM;AACb,QAAQ,QAAQ,CAAC,UAAU,GAAG,GAAG,CAAC;AAClC,QAAQ,IAAI,YAAY,EAAE,EAAE;AAC5B,UAAU,IAAI,KAAK,CAAC,aAAa,EAAE;AACnC,YAAY,CAAC,EAAE,GAAG,UAAU,IAAI,IAAI,GAAG,KAAK,CAAC,GAAG,UAAU,CAAC,QAAQ,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,UAAU,EAAE,QAAQ,CAAC,CAAC,KAAK,CAAC,CAAC,GAAG,KAAKD,eAAS,CAAC,GAAG,CAAC,CAAC,CAAC;AACrJ,WAAW;AACX,UAAU,QAAQ,CAAC,QAAQ,GAAG,GAAG,CAAC;AAClC,SAAS;AACT,OAAO;AACP,KAAK;AACL,GAAG,CAAC;AACJ,EAAE,SAAS,EAAE,CAAC;AACd,EAAEE,SAAK,CAAC,MAAM,QAAQ,CAAC,QAAQ,EAAE,CAAC,GAAG,KAAK;AAC1C,IAAI,IAAI,CAAC,GAAG,EAAE;AACd,MAAM,SAAS,EAAE,CAAC;AAClB,KAAK;AACL,GAAG,CAAC,CAAC;AACL,EAAEA,SAAK,CAAC,MAAM,KAAK,CAAC,UAAU,EAAE,CAAC,GAAG,EAAE,MAAM,KAAK;AACjD,IAAI,IAAI,QAAQ,CAAC,QAAQ,IAAIH,cAAO,CAAC,GAAG,CAAC,IAAIA,cAAO,CAAC,MAAM,CAAC,IAAI,GAAG,CAAC,KAAK,CAAC,CAAC,IAAI,EAAE,KAAK,KAAK,IAAI,KAAK,MAAM,CAAC,KAAK,CAAC,CAAC,IAAI,QAAQ,CAAC,UAAU,KAAK,GAAG,CAAC,CAAC,CAAC,IAAI,QAAQ,CAAC,WAAW,KAAK,GAAG,CAAC,CAAC,CAAC,EAAE;AACzL,MAAM,OAAO;AACb,KAAK;AACL,IAAI,SAAS,EAAE,CAAC;AAChB,GAAG,EAAE;AACL,IAAI,IAAI,EAAE,IAAI;AACd,GAAG,CAAC,CAAC;AACL,EAAEG,SAAK,CAAC,MAAM,CAAC,KAAK,CAAC,GAAG,EAAE,KAAK,CAAC,GAAG,CAAC,EAAE,MAAM;AAC5C,IAAI,SAAS,EAAE,CAAC;AAChB,GAAG,CAAC,CAAC;AACL;;;;"}