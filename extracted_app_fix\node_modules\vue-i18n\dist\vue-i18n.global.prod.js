/*!
  * vue-i18n v11.1.2
  * (c) 2025 ka<PERSON><PERSON> ka<PERSON>
  * Released under the MIT License.
  */
var VueI18n=function(e,t){"use strict";const n="undefined"!=typeof window,r=(e,t=!1)=>t?Symbol.for(e):Symbol(e),a=(e,t,n)=>o({l:e,k:t,s:n}),o=e=>JSON.stringify(e).replace(/\u2028/g,"\\u2028").replace(/\u2029/g,"\\u2029").replace(/\u0027/g,"\\u0027"),s=e=>"number"==typeof e&&isFinite(e),l=e=>"[object Date]"===y(e),c=e=>"[object RegExp]"===y(e),i=e=>T(e)&&0===Object.keys(e).length,u=Object.assign,f=Object.create,m=(e=null)=>f(e);function _(e){return e.replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/"/g,"&quot;").replace(/'/g,"&apos;")}const p=Object.prototype.hasOwnProperty;function d(e,t){return p.call(e,t)}const g=Array.isArray,E=e=>"function"==typeof e,b=e=>"string"==typeof e,h=e=>"boolean"==typeof e,k=e=>null!==e&&"object"==typeof e,L=e=>k(e)&&E(e.then)&&E(e.catch),N=Object.prototype.toString,y=e=>N.call(e),T=e=>"[object Object]"===y(e);function v(e,t=""){return e.reduce(((e,n,r)=>0===r?e+n:e+t+n),"")}function I(e,t){"undefined"!=typeof console&&(console.warn("[intlify] "+e),t&&console.warn(t.stack))}const C=e=>!k(e)||g(e);function O(e,t){if(C(e)||C(t))throw new Error("Invalid value");const n=[{src:e,des:t}];for(;n.length;){const{src:e,des:t}=n.pop();Object.keys(e).forEach((r=>{"__proto__"!==r&&(k(e[r])&&!k(t[r])&&(t[r]=Array.isArray(e[r])?[]:m()),C(t[r])||C(e[r])?t[r]=e[r]:n.push({src:e[r],des:t[r]}))}))}}function A(e,t,n){return{start:e,end:t}}const P={EXPECTED_TOKEN:1,INVALID_TOKEN_IN_PLACEHOLDER:2,UNTERMINATED_SINGLE_QUOTE_IN_PLACEHOLDER:3,UNKNOWN_ESCAPE_SEQUENCE:4,INVALID_UNICODE_ESCAPE_SEQUENCE:5,UNBALANCED_CLOSING_BRACE:6,UNTERMINATED_CLOSING_BRACE:7,EMPTY_PLACEHOLDER:8,NOT_ALLOW_NEST_PLACEHOLDER:9,INVALID_LINKED_FORMAT:10,MUST_HAVE_MESSAGES_IN_PLURAL:11,UNEXPECTED_EMPTY_LINKED_MODIFIER:12,UNEXPECTED_EMPTY_LINKED_KEY:13,UNEXPECTED_LEXICAL_ANALYSIS:14,UNHANDLED_CODEGEN_NODE_TYPE:15,UNHANDLED_MINIFIER_NODE_TYPE:16};function R(e){throw e}const S=" ",F="\r",D="\n",w=String.fromCharCode(8232),x=String.fromCharCode(8233);function M(e){const t=e;let n=0,r=1,a=1,o=0;const s=e=>t[e]===F&&t[e+1]===D,l=e=>t[e]===x,c=e=>t[e]===w,i=e=>s(e)||(e=>t[e]===D)(e)||l(e)||c(e),u=e=>s(e)||l(e)||c(e)?D:t[e];function f(){return o=0,i(n)&&(r++,a=0),s(n)&&n++,n++,a++,t[n]}return{index:()=>n,line:()=>r,column:()=>a,peekOffset:()=>o,charAt:u,currentChar:()=>u(n),currentPeek:()=>u(n+o),next:f,peek:function(){return s(n+o)&&o++,o++,t[n+o]},reset:function(){n=0,r=1,a=1,o=0},resetPeek:function(e=0){o=e},skipToPeek:function(){const e=n+o;for(;e!==n;)f();o=0}}}const U=void 0,W="'";function $(e,t={}){const n=!1!==t.location,r=M(e),a=()=>r.index(),o=()=>{return e=r.line(),t=r.column(),n=r.index(),{line:e,column:t,offset:n};var e,t,n},s=o(),l=a(),c={currentType:13,offset:l,startLoc:s,endLoc:s,lastType:13,lastOffset:l,lastStartLoc:s,lastEndLoc:s,braceNest:0,inLinked:!1,text:""},i=()=>c,{onError:u}=t;function f(e,t,r){e.endLoc=o(),e.currentType=t;const a={type:t};return n&&(a.loc=A(e.startLoc,e.endLoc)),null!=r&&(a.value=r),a}const m=e=>f(e,13);function _(e,t){return e.currentChar()===t?(e.next(),t):(P.EXPECTED_TOKEN,o(),"")}function p(e){let t="";for(;e.currentPeek()===S||e.currentPeek()===D;)t+=e.currentPeek(),e.peek();return t}function d(e){const t=p(e);return e.skipToPeek(),t}function g(e){if(e===U)return!1;const t=e.charCodeAt(0);return t>=97&&t<=122||t>=65&&t<=90||95===t}function E(e,t){const{currentType:n}=t;if(2!==n)return!1;p(e);const r=function(e){if(e===U)return!1;const t=e.charCodeAt(0);return t>=48&&t<=57}("-"===e.currentPeek()?e.peek():e.currentPeek());return e.resetPeek(),r}function b(e){p(e);const t="|"===e.currentPeek();return e.resetPeek(),t}function h(e,t=!0){const n=(t=!1,r="")=>{const a=e.currentPeek();return"{"===a?t:"@"!==a&&a?"|"===a?!(r===S||r===D):a===S?(e.peek(),n(!0,S)):a!==D||(e.peek(),n(!0,D)):t},r=n();return t&&e.resetPeek(),r}function k(e,t){const n=e.currentChar();return n===U?U:t(n)?(e.next(),n):null}function L(e){const t=e.charCodeAt(0);return t>=97&&t<=122||t>=65&&t<=90||t>=48&&t<=57||95===t||36===t}function N(e){return k(e,L)}function y(e){const t=e.charCodeAt(0);return t>=97&&t<=122||t>=65&&t<=90||t>=48&&t<=57||95===t||36===t||45===t}function T(e){return k(e,y)}function v(e){const t=e.charCodeAt(0);return t>=48&&t<=57}function I(e){return k(e,v)}function C(e){const t=e.charCodeAt(0);return t>=48&&t<=57||t>=65&&t<=70||t>=97&&t<=102}function O(e){return k(e,C)}function R(e){let t="",n="";for(;t=I(e);)n+=t;return n}function F(e){return e!==W&&e!==D}function w(e){const t=e.currentChar();switch(t){case"\\":case"'":return e.next(),`\\${t}`;case"u":return x(e,t,4);case"U":return x(e,t,6);default:return P.UNKNOWN_ESCAPE_SEQUENCE,o(),""}}function x(e,t,n){_(e,t);let r="";for(let a=0;a<n;a++){const t=O(e);if(!t){P.INVALID_UNICODE_ESCAPE_SEQUENCE,o(),e.currentChar();break}r+=t}return`\\${t}${r}`}function $(e){return"{"!==e&&"}"!==e&&e!==S&&e!==D}function H(e){d(e);const t=_(e,"|");return d(e),t}function j(e,t){let n=null;switch(e.currentChar()){case"{":return t.braceNest>=1&&(P.NOT_ALLOW_NEST_PLACEHOLDER,o()),e.next(),n=f(t,2,"{"),d(e),t.braceNest++,n;case"}":return t.braceNest>0&&2===t.currentType&&(P.EMPTY_PLACEHOLDER,o()),e.next(),n=f(t,3,"}"),t.braceNest--,t.braceNest>0&&d(e),t.inLinked&&0===t.braceNest&&(t.inLinked=!1),n;case"@":return t.braceNest>0&&(P.UNTERMINATED_CLOSING_BRACE,o()),n=V(e,t)||m(t),t.braceNest=0,n;default:{let r=!0,a=!0,s=!0;if(b(e))return t.braceNest>0&&(P.UNTERMINATED_CLOSING_BRACE,o()),n=f(t,1,H(e)),t.braceNest=0,t.inLinked=!1,n;if(t.braceNest>0&&(4===t.currentType||5===t.currentType||6===t.currentType))return P.UNTERMINATED_CLOSING_BRACE,o(),t.braceNest=0,X(e,t);if(r=function(e,t){const{currentType:n}=t;if(2!==n)return!1;p(e);const r=g(e.currentPeek());return e.resetPeek(),r}(e,t))return n=f(t,4,function(e){d(e);let t="",n="";for(;t=T(e);)n+=t;return e.currentChar()===U&&(P.UNTERMINATED_CLOSING_BRACE,o()),n}(e)),d(e),n;if(a=E(e,t))return n=f(t,5,function(e){d(e);let t="";return"-"===e.currentChar()?(e.next(),t+=`-${R(e)}`):t+=R(e),e.currentChar()===U&&(P.UNTERMINATED_CLOSING_BRACE,o()),t}(e)),d(e),n;if(s=function(e,t){const{currentType:n}=t;if(2!==n)return!1;p(e);const r=e.currentPeek()===W;return e.resetPeek(),r}(e,t))return n=f(t,6,function(e){d(e),_(e,"'");let t="",n="";for(;t=k(e,F);)n+="\\"===t?w(e):t;const r=e.currentChar();return r===D||r===U?(P.UNTERMINATED_SINGLE_QUOTE_IN_PLACEHOLDER,o(),r===D&&(e.next(),_(e,"'")),n):(_(e,"'"),n)}(e)),d(e),n;if(!r&&!a&&!s)return n=f(t,12,function(e){d(e);let t="",n="";for(;t=k(e,$);)n+=t;return n}(e)),P.INVALID_TOKEN_IN_PLACEHOLDER,o(),n.value,d(e),n;break}}return n}function V(e,t){const{currentType:n}=t;let r=null;const a=e.currentChar();switch(7!==n&&8!==n&&11!==n&&9!==n||a!==D&&a!==S||(P.INVALID_LINKED_FORMAT,o()),a){case"@":return e.next(),r=f(t,7,"@"),t.inLinked=!0,r;case".":return d(e),e.next(),f(t,8,".");case":":return d(e),e.next(),f(t,9,":");default:return b(e)?(r=f(t,1,H(e)),t.braceNest=0,t.inLinked=!1,r):function(e,t){const{currentType:n}=t;if(7!==n)return!1;p(e);const r="."===e.currentPeek();return e.resetPeek(),r}(e,t)||function(e,t){const{currentType:n}=t;if(7!==n&&11!==n)return!1;p(e);const r=":"===e.currentPeek();return e.resetPeek(),r}(e,t)?(d(e),V(e,t)):function(e,t){const{currentType:n}=t;if(8!==n)return!1;p(e);const r=g(e.currentPeek());return e.resetPeek(),r}(e,t)?(d(e),f(t,11,function(e){let t="",n="";for(;t=N(e);)n+=t;return n}(e))):function(e,t){const{currentType:n}=t;if(9!==n)return!1;const r=()=>{const t=e.currentPeek();return"{"===t?g(e.peek()):!("@"===t||"|"===t||":"===t||"."===t||t===S||!t)&&(t===D?(e.peek(),r()):h(e,!1))},a=r();return e.resetPeek(),a}(e,t)?(d(e),"{"===a?j(e,t)||r:f(t,10,function(e){const t=n=>{const r=e.currentChar();return"{"!==r&&"@"!==r&&"|"!==r&&"("!==r&&")"!==r&&r?r===S?n:(n+=r,e.next(),t(n)):n};return t("")}(e))):(7===n&&(P.INVALID_LINKED_FORMAT,o()),t.braceNest=0,t.inLinked=!1,X(e,t))}}function X(e,t){let n={type:13};if(t.braceNest>0)return j(e,t)||m(t);if(t.inLinked)return V(e,t)||m(t);switch(e.currentChar()){case"{":return j(e,t)||m(t);case"}":return P.UNBALANCED_CLOSING_BRACE,o(),e.next(),f(t,3,"}");case"@":return V(e,t)||m(t);default:if(b(e))return n=f(t,1,H(e)),t.braceNest=0,t.inLinked=!1,n;if(h(e))return f(t,0,function(e){let t="";for(;;){const n=e.currentChar();if("{"===n||"}"===n||"@"===n||"|"===n||!n)break;if(n===S||n===D)if(h(e))t+=n,e.next();else{if(b(e))break;t+=n,e.next()}else t+=n,e.next()}return t}(e))}return n}return{nextToken:function(){const{currentType:e,offset:t,startLoc:n,endLoc:s}=c;return c.lastType=e,c.lastOffset=t,c.lastStartLoc=n,c.lastEndLoc=s,c.offset=a(),c.startLoc=o(),r.currentChar()===U?f(c,13):X(r,c)},currentOffset:a,currentPosition:o,context:i}}const H=/(?:\\\\|\\'|\\u([0-9a-fA-F]{4})|\\U([0-9a-fA-F]{6}))/g;function j(e,t,n){switch(e){case"\\\\":return"\\";case"\\'":return"'";default:{const e=parseInt(t||n,16);return e<=55295||e>=57344?String.fromCodePoint(e):"�"}}}function V(e={}){const t=!1!==e.location,{onError:n}=e;function r(e,n,r){const a={type:e};return t&&(a.start=n,a.end=n,a.loc={start:r,end:r}),a}function a(e,n,r,a){t&&(e.end=n,e.loc&&(e.loc.end=r))}function o(e,t){const n=e.context(),o=r(3,n.offset,n.startLoc);return o.value=t,a(o,e.currentOffset(),e.currentPosition()),o}function s(e,t){const n=e.context(),{lastOffset:o,lastStartLoc:s}=n,l=r(5,o,s);return l.index=parseInt(t,10),e.nextToken(),a(l,e.currentOffset(),e.currentPosition()),l}function l(e,t){const n=e.context(),{lastOffset:o,lastStartLoc:s}=n,l=r(4,o,s);return l.key=t,e.nextToken(),a(l,e.currentOffset(),e.currentPosition()),l}function c(e,t){const n=e.context(),{lastOffset:o,lastStartLoc:s}=n,l=r(9,o,s);return l.value=t.replace(H,j),e.nextToken(),a(l,e.currentOffset(),e.currentPosition()),l}function i(e){const t=e.context(),n=r(6,t.offset,t.startLoc);let o=e.nextToken();if(8===o.type){const t=function(e){const t=e.nextToken(),n=e.context(),{lastOffset:o,lastStartLoc:s}=n,l=r(8,o,s);return 11!==t.type?(P.UNEXPECTED_EMPTY_LINKED_MODIFIER,n.lastStartLoc,l.value="",a(l,o,s),{nextConsumeToken:t,node:l}):(null==t.value&&(P.UNEXPECTED_LEXICAL_ANALYSIS,n.lastStartLoc,X(t)),l.value=t.value||"",a(l,e.currentOffset(),e.currentPosition()),{node:l})}(e);n.modifier=t.node,o=t.nextConsumeToken||e.nextToken()}switch(9!==o.type&&(P.UNEXPECTED_LEXICAL_ANALYSIS,t.lastStartLoc,X(o)),o=e.nextToken(),2===o.type&&(o=e.nextToken()),o.type){case 10:null==o.value&&(P.UNEXPECTED_LEXICAL_ANALYSIS,t.lastStartLoc,X(o)),n.key=function(e,t){const n=e.context(),o=r(7,n.offset,n.startLoc);return o.value=t,a(o,e.currentOffset(),e.currentPosition()),o}(e,o.value||"");break;case 4:null==o.value&&(P.UNEXPECTED_LEXICAL_ANALYSIS,t.lastStartLoc,X(o)),n.key=l(e,o.value||"");break;case 5:null==o.value&&(P.UNEXPECTED_LEXICAL_ANALYSIS,t.lastStartLoc,X(o)),n.key=s(e,o.value||"");break;case 6:null==o.value&&(P.UNEXPECTED_LEXICAL_ANALYSIS,t.lastStartLoc,X(o)),n.key=c(e,o.value||"");break;default:{P.UNEXPECTED_EMPTY_LINKED_KEY,t.lastStartLoc;const s=e.context(),l=r(7,s.offset,s.startLoc);return l.value="",a(l,s.offset,s.startLoc),n.key=l,a(n,s.offset,s.startLoc),{nextConsumeToken:o,node:n}}}return a(n,e.currentOffset(),e.currentPosition()),{node:n}}function f(e){const t=e.context(),n=r(2,1===t.currentType?e.currentOffset():t.offset,1===t.currentType?t.endLoc:t.startLoc);n.items=[];let u=null;do{const r=u||e.nextToken();switch(u=null,r.type){case 0:null==r.value&&(P.UNEXPECTED_LEXICAL_ANALYSIS,t.lastStartLoc,X(r)),n.items.push(o(e,r.value||""));break;case 5:null==r.value&&(P.UNEXPECTED_LEXICAL_ANALYSIS,t.lastStartLoc,X(r)),n.items.push(s(e,r.value||""));break;case 4:null==r.value&&(P.UNEXPECTED_LEXICAL_ANALYSIS,t.lastStartLoc,X(r)),n.items.push(l(e,r.value||""));break;case 6:null==r.value&&(P.UNEXPECTED_LEXICAL_ANALYSIS,t.lastStartLoc,X(r)),n.items.push(c(e,r.value||""));break;case 7:{const t=i(e);n.items.push(t.node),u=t.nextConsumeToken||null;break}}}while(13!==t.currentType&&1!==t.currentType);return a(n,1===t.currentType?t.lastOffset:e.currentOffset(),1===t.currentType?t.lastEndLoc:e.currentPosition()),n}function m(e){const t=e.context(),{offset:n,startLoc:o}=t,s=f(e);return 13===t.currentType?s:function(e,t,n,o){const s=e.context();let l=0===o.items.length;const c=r(1,t,n);c.cases=[],c.cases.push(o);do{const t=f(e);l||(l=0===t.items.length),c.cases.push(t)}while(13!==s.currentType);return a(c,e.currentOffset(),e.currentPosition()),c}(e,n,o,s)}return{parse:function(n){const o=$(n,u({},e)),s=o.context(),l=r(0,s.offset,s.startLoc);return t&&l.loc&&(l.loc.source=n),l.body=m(o),e.onCacheKey&&(l.cacheKey=e.onCacheKey(n)),13!==s.currentType&&(P.UNEXPECTED_LEXICAL_ANALYSIS,s.lastStartLoc,n[s.offset]),a(l,o.currentOffset(),o.currentPosition()),l}}}function X(e){if(13===e.type)return"EOF";const t=(e.value||"").replace(/\r?\n/gu,"\\n");return t.length>10?t.slice(0,9)+"…":t}function Y(e,t){for(let n=0;n<e.length;n++)G(e[n],t)}function G(e,t){switch(e.type){case 1:Y(e.cases,t),t.helper("plural");break;case 2:Y(e.items,t);break;case 6:G(e.key,t),t.helper("linked"),t.helper("type");break;case 5:t.helper("interpolate"),t.helper("list");break;case 4:t.helper("interpolate"),t.helper("named")}}function K(e,t={}){const n=function(e,t={}){const n={ast:e,helpers:new Set};return{context:()=>n,helper:e=>(n.helpers.add(e),e)}}(e);n.helper("normalize"),e.body&&G(e.body,n);const r=n.context();e.helpers=Array.from(r.helpers)}function B(e){if(1===e.items.length){const t=e.items[0];3!==t.type&&9!==t.type||(e.static=t.value,delete t.value)}else{const t=[];for(let n=0;n<e.items.length;n++){const r=e.items[n];if(3!==r.type&&9!==r.type)break;if(null==r.value)break;t.push(r.value)}if(t.length===e.items.length){e.static=v(t);for(let t=0;t<e.items.length;t++){const n=e.items[t];3!==n.type&&9!==n.type||delete n.value}}}}function z(e){switch(e.t=e.type,e.type){case 0:{const t=e;z(t.body),t.b=t.body,delete t.body;break}case 1:{const t=e,n=t.cases;for(let e=0;e<n.length;e++)z(n[e]);t.c=n,delete t.cases;break}case 2:{const t=e,n=t.items;for(let e=0;e<n.length;e++)z(n[e]);t.i=n,delete t.items,t.static&&(t.s=t.static,delete t.static);break}case 3:case 9:case 8:case 7:{const t=e;t.value&&(t.v=t.value,delete t.value);break}case 6:{const t=e;z(t.key),t.k=t.key,delete t.key,t.modifier&&(z(t.modifier),t.m=t.modifier,delete t.modifier);break}case 5:{const t=e;t.i=t.index,delete t.index;break}case 4:{const t=e;t.k=t.key,delete t.key;break}}delete e.type}function J(e,t){const{helper:n}=e;switch(t.type){case 0:!function(e,t){t.body?J(e,t.body):e.push("null")}(e,t);break;case 1:!function(e,t){const{helper:n,needIndent:r}=e;if(t.cases.length>1){e.push(`${n("plural")}([`),e.indent(r());const a=t.cases.length;for(let n=0;n<a&&(J(e,t.cases[n]),n!==a-1);n++)e.push(", ");e.deindent(r()),e.push("])")}}(e,t);break;case 2:!function(e,t){const{helper:n,needIndent:r}=e;e.push(`${n("normalize")}([`),e.indent(r());const a=t.items.length;for(let o=0;o<a&&(J(e,t.items[o]),o!==a-1);o++)e.push(", ");e.deindent(r()),e.push("])")}(e,t);break;case 6:!function(e,t){const{helper:n}=e;e.push(`${n("linked")}(`),J(e,t.key),t.modifier?(e.push(", "),J(e,t.modifier),e.push(", _type")):e.push(", undefined, _type"),e.push(")")}(e,t);break;case 8:case 7:case 9:case 3:e.push(JSON.stringify(t.value),t);break;case 5:e.push(`${n("interpolate")}(${n("list")}(${t.index}))`,t);break;case 4:e.push(`${n("interpolate")}(${n("named")}(${JSON.stringify(t.key)}))`,t)}}const Q=(e,t={})=>{const n=b(t.mode)?t.mode:"normal",r=b(t.filename)?t.filename:"message.intl",a=!!t.sourceMap,o=null!=t.breakLineCode?t.breakLineCode:"arrow"===n?";":"\n",s=t.needIndent?t.needIndent:"arrow"!==n,l=e.helpers||[],c=function(e,t){const{sourceMap:n,filename:r,breakLineCode:a,needIndent:o}=t,s=!1!==t.location,l={filename:r,code:"",column:1,line:1,offset:0,map:void 0,breakLineCode:a,needIndent:o,indentLevel:0};function c(e,t){l.code+=e}function i(e,t=!0){const n=t?a:"";c(o?n+"  ".repeat(e):n)}return s&&e.loc&&(l.source=e.loc.source),{context:()=>l,push:c,indent:function(e=!0){const t=++l.indentLevel;e&&i(t)},deindent:function(e=!0){const t=--l.indentLevel;e&&i(t)},newline:function(){i(l.indentLevel)},helper:e=>`_${e}`,needIndent:()=>l.needIndent}}(e,{mode:n,filename:r,sourceMap:a,breakLineCode:o,needIndent:s});c.push("normal"===n?"function __msg__ (ctx) {":"(ctx) => {"),c.indent(s),l.length>0&&(c.push(`const { ${v(l.map((e=>`${e}: _${e}`)),", ")} } = ctx`),c.newline()),c.push("return "),J(c,e),c.deindent(s),c.push("}"),delete e.helpers;const{code:i,map:u}=c.context();return{ast:e,code:i,map:u?u.toJSON():void 0}};function q(e,t={}){const n=u({},t),r=!!n.jit,a=!!n.minify,o=null==n.optimize||n.optimize,s=V(n).parse(e);return r?(o&&function(e){const t=e.body;2===t.type?B(t):t.cases.forEach((e=>B(e)))}(s),a&&z(s),{ast:s,code:""}):(K(s,n),Q(s,n))}function Z(e){return t=>function(e,t){const n=(r=t,me(r,ee));var r;if(null==n)throw _e(0);if(1===le(n)){const t=function(e){return me(e,te,[])}(n);return e.plural(t.reduce(((t,n)=>[...t,ne(e,n)]),[]))}return ne(e,n)}(t,e)}const ee=["b","body"];const te=["c","cases"];function ne(e,t){const n=function(e){return me(e,re)}(t);if(null!=n)return"text"===e.type?n:e.normalize([n]);{const n=function(e){return me(e,ae,[])}(t).reduce(((t,n)=>[...t,oe(e,n)]),[]);return e.normalize(n)}}const re=["s","static"];const ae=["i","items"];function oe(e,t){const n=le(t);switch(n){case 3:case 9:case 7:case 8:return ie(t,n);case 4:{const r=t;if(d(r,"k")&&r.k)return e.interpolate(e.named(r.k));if(d(r,"key")&&r.key)return e.interpolate(e.named(r.key));throw _e(n)}case 5:{const r=t;if(d(r,"i")&&s(r.i))return e.interpolate(e.list(r.i));if(d(r,"index")&&s(r.index))return e.interpolate(e.list(r.index));throw _e(n)}case 6:{const n=t,r=function(e){return me(e,ue)}(n),a=function(e){const t=me(e,fe);if(t)return t;throw _e(6)}(n);return e.linked(oe(e,a),r?oe(e,r):void 0,e.type)}default:throw new Error(`unhandled node on format message part: ${n}`)}}const se=["t","type"];function le(e){return me(e,se)}const ce=["v","value"];function ie(e,t){const n=me(e,ce);if(n)return n;throw _e(t)}const ue=["m","modifier"];const fe=["k","key"];function me(e,t,n){for(let r=0;r<t.length;r++){const n=t[r];if(d(e,n)&&null!=e[n])return e[n]}return n}function _e(e){return new Error(`unhandled node type: ${e}`)}const pe=e=>e;let de=m();function ge(e){return k(e)&&0===le(e)&&(d(e,"b")||d(e,"body"))}const Ee={INVALID_ARGUMENT:17,INVALID_DATE_ARGUMENT:18,INVALID_ISO_DATE_ARGUMENT:19,NOT_SUPPORT_NON_STRING_MESSAGE:20,NOT_SUPPORT_LOCALE_PROMISE_VALUE:21,NOT_SUPPORT_LOCALE_ASYNC_FUNCTION:22,NOT_SUPPORT_LOCALE_TYPE:23};function be(e,t){return null!=t.locale?ke(t.locale):ke(e.locale)}let he;function ke(e){if(b(e))return e;if(E(e)){if(e.resolvedOnce&&null!=he)return he;if("Function"===e.constructor.name){const t=e();if(L(t))throw Error(Ee.NOT_SUPPORT_LOCALE_PROMISE_VALUE);return he=t}throw Error(Ee.NOT_SUPPORT_LOCALE_ASYNC_FUNCTION)}throw Error(Ee.NOT_SUPPORT_LOCALE_TYPE)}function Le(e,t,n){return[...new Set([n,...g(t)?t:k(t)?Object.keys(t):b(t)?[t]:[n]])]}function Ne(e,t,n){const r=b(n)?n:De,a=e;a.__localeChainCache||(a.__localeChainCache=new Map);let o=a.__localeChainCache.get(r);if(!o){o=[];let e=[n];for(;g(e);)e=ye(o,e,t);const s=g(t)||!T(t)?t:t.default?t.default:null;e=b(s)?[s]:s,g(e)&&ye(o,e,!1),a.__localeChainCache.set(r,o)}return o}function ye(e,t,n){let r=!0;for(let a=0;a<t.length&&h(r);a++){const o=t[a];b(o)&&(r=Te(e,t[a],n))}return r}function Te(e,t,n){let r;const a=t.split("-");do{r=ve(e,a.join("-"),n),a.splice(-1,1)}while(a.length&&!0===r);return r}function ve(e,t,n){let r=!1;if(!e.includes(t)&&(r=!0,t)){r="!"!==t[t.length-1];const a=t.replace(/!/g,"");e.push(a),(g(n)||T(n))&&n[a]&&(r=n[a])}return r}const Ie=[];Ie[0]={w:[0],i:[3,0],"[":[4],o:[7]},Ie[1]={w:[1],".":[2],"[":[4],o:[7]},Ie[2]={w:[2],i:[3,0],0:[3,0]},Ie[3]={i:[3,0],0:[3,0],w:[1,1],".":[2,1],"[":[4,1],o:[7,1]},Ie[4]={"'":[5,0],'"':[6,0],"[":[4,2],"]":[1,3],o:8,l:[4,0]},Ie[5]={"'":[4,0],o:8,l:[5,0]},Ie[6]={'"':[4,0],o:8,l:[6,0]};const Ce=/^\s?(?:true|false|-?[\d.]+|'[^']*'|"[^"]*")\s?$/;function Oe(e){if(null==e)return"o";switch(e.charCodeAt(0)){case 91:case 93:case 46:case 34:case 39:return e;case 95:case 36:case 45:return"i";case 9:case 10:case 13:case 160:case 65279:case 8232:case 8233:return"w"}return"i"}function Ae(e){const t=e.trim();return("0"!==e.charAt(0)||!isNaN(parseInt(e)))&&(n=t,Ce.test(n)?function(e){const t=e.charCodeAt(0);return t!==e.charCodeAt(e.length-1)||34!==t&&39!==t?e:e.slice(1,-1)}(t):"*"+t);var n}const Pe=new Map;function Re(e,t){return k(e)?e[t]:null}const Se="11.1.2",Fe=-1,De="en-US",we="",xe=e=>`${e.charAt(0).toLocaleUpperCase()}${e.substr(1)}`;let Me,Ue,We;let $e=null;const He=e=>{$e=e},je=()=>$e;let Ve=0;function Xe(e={}){const t=E(e.onWarn)?e.onWarn:I,n=b(e.version)?e.version:Se,r=b(e.locale)||E(e.locale)?e.locale:De,a=E(r)?De:r,o=g(e.fallbackLocale)||T(e.fallbackLocale)||b(e.fallbackLocale)||!1===e.fallbackLocale?e.fallbackLocale:a,s=T(e.messages)?e.messages:Ye(a),l=T(e.datetimeFormats)?e.datetimeFormats:Ye(a),i=T(e.numberFormats)?e.numberFormats:Ye(a),f=u(m(),e.modifiers,{upper:(e,t)=>"text"===t&&b(e)?e.toUpperCase():"vnode"===t&&k(e)&&"__v_isVNode"in e?e.children.toUpperCase():e,lower:(e,t)=>"text"===t&&b(e)?e.toLowerCase():"vnode"===t&&k(e)&&"__v_isVNode"in e?e.children.toLowerCase():e,capitalize:(e,t)=>"text"===t&&b(e)?xe(e):"vnode"===t&&k(e)&&"__v_isVNode"in e?xe(e.children):e}),_=e.pluralRules||m(),p=E(e.missing)?e.missing:null,d=!h(e.missingWarn)&&!c(e.missingWarn)||e.missingWarn,L=!h(e.fallbackWarn)&&!c(e.fallbackWarn)||e.fallbackWarn,N=!!e.fallbackFormat,y=!!e.unresolving,v=E(e.postTranslation)?e.postTranslation:null,C=T(e.processor)?e.processor:null,O=!h(e.warnHtmlMessage)||e.warnHtmlMessage,A=!!e.escapeParameter,P=E(e.messageCompiler)?e.messageCompiler:Me,R=E(e.messageResolver)?e.messageResolver:Ue||Re,S=E(e.localeFallbacker)?e.localeFallbacker:We||Le,F=k(e.fallbackContext)?e.fallbackContext:void 0,D=e,w=k(D.__datetimeFormatters)?D.__datetimeFormatters:new Map,x=k(D.__numberFormatters)?D.__numberFormatters:new Map,M=k(D.__meta)?D.__meta:{};Ve++;const U={version:n,cid:Ve,locale:r,fallbackLocale:o,messages:s,modifiers:f,pluralRules:_,missing:p,missingWarn:d,fallbackWarn:L,fallbackFormat:N,unresolving:y,postTranslation:v,processor:C,warnHtmlMessage:O,escapeParameter:A,messageCompiler:P,messageResolver:R,localeFallbacker:S,fallbackContext:F,onWarn:t,__meta:M};return U.datetimeFormats=l,U.numberFormats=i,U.__datetimeFormatters=w,U.__numberFormatters=x,U}const Ye=e=>({[e]:m()});function Ge(e,t,n,r,a){const{missing:o,onWarn:s}=e;if(null!==o){const r=o(e,n,t,a);return b(r)?r:t}return t}function Ke(e,t,n){e.__localeChainCache=new Map,e.localeFallbacker(e,n,t)}function Be(e,t){const n=t.indexOf(e);if(-1===n)return!1;for(let o=n+1;o<t.length;o++)if(r=e,a=t[o],r!==a&&r.split("-")[0]===a.split("-")[0])return!0;var r,a;return!1}function ze(e,...t){const{datetimeFormats:n,unresolving:r,fallbackLocale:a,onWarn:o,localeFallbacker:s}=e,{__datetimeFormatters:l}=e,[c,f,m,_]=Qe(...t);h(m.missingWarn)?m.missingWarn:e.missingWarn;h(m.fallbackWarn)?m.fallbackWarn:e.fallbackWarn;const p=!!m.part,d=be(e,m),g=s(e,a,d);if(!b(c)||""===c)return new Intl.DateTimeFormat(d,_).format(f);let E,k={},L=null;for(let i=0;i<g.length&&(E=g[i],k=n[E]||{},L=k[c],!T(L));i++)Ge(e,c,E,0,"datetime format");if(!T(L)||!b(E))return r?Fe:c;let N=`${E}__${c}`;i(_)||(N=`${N}__${JSON.stringify(_)}`);let y=l.get(N);return y||(y=new Intl.DateTimeFormat(E,u({},L,_)),l.set(N,y)),p?y.formatToParts(f):y.format(f)}const Je=["localeMatcher","weekday","era","year","month","day","hour","minute","second","timeZoneName","formatMatcher","hour12","timeZone","dateStyle","timeStyle","calendar","dayPeriod","numberingSystem","hourCycle","fractionalSecondDigits"];function Qe(...e){const[t,n,r,a]=e,o=m();let c,i=m();if(b(t)){const e=t.match(/(\d{4}-\d{2}-\d{2})(T|\s)?(.*)/);if(!e)throw Error(Ee.INVALID_ISO_DATE_ARGUMENT);const n=e[3]?e[3].trim().startsWith("T")?`${e[1].trim()}${e[3].trim()}`:`${e[1].trim()}T${e[3].trim()}`:e[1].trim();c=new Date(n);try{c.toISOString()}catch{throw Error(Ee.INVALID_ISO_DATE_ARGUMENT)}}else if(l(t)){if(isNaN(t.getTime()))throw Error(Ee.INVALID_DATE_ARGUMENT);c=t}else{if(!s(t))throw Error(Ee.INVALID_ARGUMENT);c=t}return b(n)?o.key=n:T(n)&&Object.keys(n).forEach((e=>{Je.includes(e)?i[e]=n[e]:o[e]=n[e]})),b(r)?o.locale=r:T(r)&&(i=r),T(a)&&(i=a),[o.key||"",c,o,i]}function qe(e,t,n){const r=e;for(const a in n){const e=`${t}__${a}`;r.__datetimeFormatters.has(e)&&r.__datetimeFormatters.delete(e)}}function Ze(e,...t){const{numberFormats:n,unresolving:r,fallbackLocale:a,onWarn:o,localeFallbacker:s}=e,{__numberFormatters:l}=e,[c,f,m,_]=tt(...t);h(m.missingWarn)?m.missingWarn:e.missingWarn;h(m.fallbackWarn)?m.fallbackWarn:e.fallbackWarn;const p=!!m.part,d=be(e,m),g=s(e,a,d);if(!b(c)||""===c)return new Intl.NumberFormat(d,_).format(f);let E,k={},L=null;for(let i=0;i<g.length&&(E=g[i],k=n[E]||{},L=k[c],!T(L));i++)Ge(e,c,E,0,"number format");if(!T(L)||!b(E))return r?Fe:c;let N=`${E}__${c}`;i(_)||(N=`${N}__${JSON.stringify(_)}`);let y=l.get(N);return y||(y=new Intl.NumberFormat(E,u({},L,_)),l.set(N,y)),p?y.formatToParts(f):y.format(f)}const et=["localeMatcher","style","currency","currencyDisplay","currencySign","useGrouping","minimumIntegerDigits","minimumFractionDigits","maximumFractionDigits","minimumSignificantDigits","maximumSignificantDigits","compactDisplay","notation","signDisplay","unit","unitDisplay","roundingMode","roundingPriority","roundingIncrement","trailingZeroDisplay"];function tt(...e){const[t,n,r,a]=e,o=m();let l=m();if(!s(t))throw Error(Ee.INVALID_ARGUMENT);const c=t;return b(n)?o.key=n:T(n)&&Object.keys(n).forEach((e=>{et.includes(e)?l[e]=n[e]:o[e]=n[e]})),b(r)?o.locale=r:T(r)&&(l=r),T(a)&&(l=a),[o.key||"",c,o,l]}function nt(e,t,n){const r=e;for(const a in n){const e=`${t}__${a}`;r.__numberFormatters.has(e)&&r.__numberFormatters.delete(e)}}const rt=e=>e,at=e=>"",ot="text",st=e=>0===e.length?"":v(e),lt=e=>null==e?"":g(e)||T(e)&&e.toString===N?JSON.stringify(e,null,2):String(e);function ct(e,t){return e=Math.abs(e),2===t?e?e>1?1:0:1:e?Math.min(e,2):0}function it(e={}){const t=e.locale,n=function(e){const t=s(e.pluralIndex)?e.pluralIndex:-1;return e.named&&(s(e.named.count)||s(e.named.n))?s(e.named.count)?e.named.count:s(e.named.n)?e.named.n:t:t}(e),r=k(e.pluralRules)&&b(t)&&E(e.pluralRules[t])?e.pluralRules[t]:ct,a=k(e.pluralRules)&&b(t)&&E(e.pluralRules[t])?ct:void 0,o=e.list||[],l=e.named||m();s(e.pluralIndex)&&function(e,t){t.count||(t.count=e),t.n||(t.n=e)}(n,l);function c(t,n){const r=E(e.messages)?e.messages(t,!!n):!!k(e.messages)&&e.messages[t];return r||(e.parent?e.parent.message(t):at)}const i=T(e.processor)&&E(e.processor.normalize)?e.processor.normalize:st,f=T(e.processor)&&E(e.processor.interpolate)?e.processor.interpolate:lt,_={list:e=>o[e],named:e=>l[e],plural:e=>e[r(n,e.length,a)],linked:(t,...n)=>{const[r,a]=n;let o="text",s="";1===n.length?k(r)?(s=r.modifier||s,o=r.type||o):b(r)&&(s=r||s):2===n.length&&(b(r)&&(s=r||s),b(a)&&(o=a||o));const l=c(t,!0)(_),i="vnode"===o&&g(l)&&s?l[0]:l;return s?(u=s,e.modifiers?e.modifiers[u]:rt)(i,o):i;var u},message:c,type:T(e.processor)&&b(e.processor.type)?e.processor.type:ot,interpolate:f,normalize:i,values:u(m(),o,l)};return _}const ut=()=>"",ft=e=>E(e);function mt(e,...t){const{fallbackFormat:n,postTranslation:r,unresolving:a,messageCompiler:o,fallbackLocale:l,messages:c}=e,[i,u]=dt(...t),f=h(u.missingWarn)?u.missingWarn:e.missingWarn,p=h(u.fallbackWarn)?u.fallbackWarn:e.fallbackWarn,d=h(u.escapeParameter)?u.escapeParameter:e.escapeParameter,L=!!u.resolvedMessage,N=b(u.default)||h(u.default)?h(u.default)?o?i:()=>i:u.default:n?o?i:()=>i:null,y=n||null!=N&&(b(N)||E(N)),T=be(e,u);d&&function(e){g(e.list)?e.list=e.list.map((e=>b(e)?_(e):e)):k(e.named)&&Object.keys(e.named).forEach((t=>{b(e.named[t])&&(e.named[t]=_(e.named[t]))}))}(u);let[v,I,C]=L?[i,T,c[T]||m()]:_t(e,i,T,l,p,f),O=v,A=i;if(L||b(O)||ge(O)||ft(O)||y&&(O=N,A=O),!(L||(b(O)||ge(O)||ft(O))&&b(I)))return a?Fe:i;let P=!1;const R=ft(O)?O:pt(e,i,I,O,A,(()=>{P=!0}));if(P)return O;const S=function(e,t,n,r){const{modifiers:a,pluralRules:o,messageResolver:l,fallbackLocale:c,fallbackWarn:i,missingWarn:u,fallbackContext:f}=e,m=(r,a)=>{let o=l(n,r);if(null==o&&(f||a)){const[,,n]=_t(f||e,r,t,c,i,u);o=l(n,r)}if(b(o)||ge(o)){let n=!1;const a=pt(e,r,t,o,r,(()=>{n=!0}));return n?ut:a}return ft(o)?o:ut},_={locale:t,modifiers:a,pluralRules:o,messages:m};e.processor&&(_.processor=e.processor);r.list&&(_.list=r.list);r.named&&(_.named=r.named);s(r.plural)&&(_.pluralIndex=r.plural);return _}(e,I,C,u),F=function(e,t,n){const r=t(n);return r}(0,R,it(S));return r?r(F,i):F}function _t(e,t,n,r,a,o){const{messages:s,onWarn:l,messageResolver:c,localeFallbacker:i}=e,u=i(e,r,n);let f,_=m(),p=null;for(let d=0;d<u.length&&(f=u[d],_=s[f]||m(),null===(p=c(_,t))&&(p=_[t]),!(b(p)||ge(p)||ft(p)));d++)if(!Be(f,u)){const n=Ge(e,t,f,0,"translate");n!==t&&(p=n)}return[p,f,_]}function pt(e,t,n,r,o,s){const{messageCompiler:l,warnHtmlMessage:c}=e;if(ft(r)){const e=r;return e.locale=e.locale||n,e.key=e.key||t,e}if(null==l){const e=()=>r;return e.locale=n,e.key=t,e}const i=l(r,function(e,t,n,r,o,s){return{locale:t,key:n,warnHtmlMessage:o,onError:e=>{throw s&&s(e),e},onCacheKey:e=>a(t,n,e)}}(0,n,o,0,c,s));return i.locale=n,i.key=t,i.source=r,i}function dt(...e){const[t,n,r]=e,a=m();if(!(b(t)||s(t)||ft(t)||ge(t)))throw Error(Ee.INVALID_ARGUMENT);const o=s(t)?String(t):(ft(t),t);return s(n)?a.plural=n:b(n)?a.default=n:T(n)&&!i(n)?a.named=n:g(n)&&(a.list=n),s(r)?a.plural=r:b(r)?a.default=r:T(r)&&u(a,r),[o,a]}const gt="11.1.2",Et={UNEXPECTED_RETURN_TYPE:24,INVALID_ARGUMENT:25,MUST_BE_CALL_SETUP_TOP:26,NOT_INSTALLED:27,REQUIRED_VALUE:28,INVALID_VALUE:29,CANNOT_SETUP_VUE_DEVTOOLS_PLUGIN:30,NOT_INSTALLED_WITH_PROVIDE:31,UNEXPECTED_ERROR:32,NOT_COMPATIBLE_LEGACY_VUE_I18N:33,NOT_AVAILABLE_COMPOSITION_IN_LEGACY:34},bt=r("__translateVNode"),ht=r("__datetimeParts"),kt=r("__numberParts"),Lt=r("__setPluralRules"),Nt=r("__injectWithOption"),yt=r("__dispose");function Tt(e){if(!k(e))return e;for(const t in e)if(d(e,t))if(t.includes(".")){const n=t.split("."),r=n.length-1;let a=e,o=!1;for(let e=0;e<r;e++){if("__proto__"===n[e])throw new Error(`unsafe key: ${n[e]}`);if(n[e]in a||(a[n[e]]=m()),!k(a[n[e]])){o=!0;break}a=a[n[e]]}o||(a[n[r]]=e[t],delete e[t]),k(a[n[r]])&&Tt(a[n[r]])}else k(e[t])&&Tt(e[t]);return e}function vt(e,t){const{messages:n,__i18n:r,messageResolver:a,flatJson:o}=t,s=T(n)?n:g(r)?m():{[e]:m()};if(g(r)&&r.forEach((e=>{if("locale"in e&&"resource"in e){const{locale:t,resource:n}=e;t?(s[t]=s[t]||m(),O(n,s[t])):O(n,s)}else b(e)&&O(JSON.parse(e),s)})),null==a&&o)for(const l in s)d(s,l)&&Tt(s[l]);return s}function It(e){return e.type}function Ct(e,t,n){let r=k(t.messages)?t.messages:m();"__i18nGlobal"in n&&(r=vt(e.locale.value,{messages:r,__i18n:n.__i18nGlobal}));const a=Object.keys(r);if(a.length&&a.forEach((t=>{e.mergeLocaleMessage(t,r[t])})),k(t.datetimeFormats)){const n=Object.keys(t.datetimeFormats);n.length&&n.forEach((n=>{e.mergeDateTimeFormat(n,t.datetimeFormats[n])}))}if(k(t.numberFormats)){const n=Object.keys(t.numberFormats);n.length&&n.forEach((n=>{e.mergeNumberFormat(n,t.numberFormats[n])}))}}function Ot(e){return t.createVNode(t.Text,null,e,0)}const At=()=>[],Pt=()=>!1;let Rt=0;function St(e){return(n,r,a,o)=>e(r,a,t.getCurrentInstance()||void 0,o)}function Ft(e={}){const{__root:r,__injectWithOption:a}=e,o=void 0===r,l=e.flatJson,i=n?t.ref:t.shallowRef;let f=!h(e.inheritLocale)||e.inheritLocale;const m=i(r&&f?r.locale.value:b(e.locale)?e.locale:De),_=i(r&&f?r.fallbackLocale.value:b(e.fallbackLocale)||g(e.fallbackLocale)||T(e.fallbackLocale)||!1===e.fallbackLocale?e.fallbackLocale:m.value),p=i(vt(m.value,e)),L=i(T(e.datetimeFormats)?e.datetimeFormats:{[m.value]:{}}),N=i(T(e.numberFormats)?e.numberFormats:{[m.value]:{}});let y=r?r.missingWarn:!h(e.missingWarn)&&!c(e.missingWarn)||e.missingWarn,v=r?r.fallbackWarn:!h(e.fallbackWarn)&&!c(e.fallbackWarn)||e.fallbackWarn,I=r?r.fallbackRoot:!h(e.fallbackRoot)||e.fallbackRoot,C=!!e.fallbackFormat,A=E(e.missing)?e.missing:null,P=E(e.missing)?St(e.missing):null,R=E(e.postTranslation)?e.postTranslation:null,S=r?r.warnHtmlMessage:!h(e.warnHtmlMessage)||e.warnHtmlMessage,F=!!e.escapeParameter;const D=r?r.modifiers:T(e.modifiers)?e.modifiers:{};let w,x=e.pluralRules||r&&r.pluralRules;w=(()=>{o&&He(null);const t={version:gt,locale:m.value,fallbackLocale:_.value,messages:p.value,modifiers:D,pluralRules:x,missing:null===P?void 0:P,missingWarn:y,fallbackWarn:v,fallbackFormat:C,unresolving:!0,postTranslation:null===R?void 0:R,warnHtmlMessage:S,escapeParameter:F,messageResolver:e.messageResolver,messageCompiler:e.messageCompiler,__meta:{framework:"vue"}};t.datetimeFormats=L.value,t.numberFormats=N.value,t.__datetimeFormatters=T(w)?w.__datetimeFormatters:void 0,t.__numberFormatters=T(w)?w.__numberFormatters:void 0;const n=Xe(t);return o&&He(n),n})(),Ke(w,m.value,_.value);const M=t.computed({get:()=>m.value,set:e=>{w.locale=e,m.value=e}}),U=t.computed({get:()=>_.value,set:e=>{w.fallbackLocale=e,_.value=e,Ke(w,m.value,e)}}),W=t.computed((()=>p.value)),$=t.computed((()=>L.value)),H=t.computed((()=>N.value));const j=(e,t,n,a,l,c)=>{let i;m.value,_.value,p.value,L.value,N.value;try{0,o||(w.fallbackContext=r?je():void 0),i=e(w)}finally{o||(w.fallbackContext=void 0)}if("translate exists"!==n&&s(i)&&i===Fe||"translate exists"===n&&!i){const[e,n]=t();return r&&I?a(r):l(e)}if(c(i))return i;throw Error(Et.UNEXPECTED_RETURN_TYPE)};function V(...e){return j((t=>Reflect.apply(mt,null,[t,...e])),(()=>dt(...e)),"translate",(t=>Reflect.apply(t.t,t,[...e])),(e=>e),(e=>b(e)))}const X={normalize:function(e){return e.map((e=>b(e)||s(e)||h(e)?Ot(String(e)):e))},interpolate:e=>e,type:"vnode"};function Y(e){return p.value[e]||{}}Rt++,r&&n&&(t.watch(r.locale,(e=>{f&&(m.value=e,w.locale=e,Ke(w,m.value,_.value))})),t.watch(r.fallbackLocale,(e=>{f&&(_.value=e,w.fallbackLocale=e,Ke(w,m.value,_.value))})));const G={id:Rt,locale:M,fallbackLocale:U,get inheritLocale(){return f},set inheritLocale(e){f=e,e&&r&&(m.value=r.locale.value,_.value=r.fallbackLocale.value,Ke(w,m.value,_.value))},get availableLocales(){return Object.keys(p.value).sort()},messages:W,get modifiers(){return D},get pluralRules(){return x||{}},get isGlobal(){return o},get missingWarn(){return y},set missingWarn(e){y=e,w.missingWarn=y},get fallbackWarn(){return v},set fallbackWarn(e){v=e,w.fallbackWarn=v},get fallbackRoot(){return I},set fallbackRoot(e){I=e},get fallbackFormat(){return C},set fallbackFormat(e){C=e,w.fallbackFormat=C},get warnHtmlMessage(){return S},set warnHtmlMessage(e){S=e,w.warnHtmlMessage=e},get escapeParameter(){return F},set escapeParameter(e){F=e,w.escapeParameter=e},t:V,getLocaleMessage:Y,setLocaleMessage:function(e,t){if(l){const n={[e]:t};for(const e in n)d(n,e)&&Tt(n[e]);t=n[e]}p.value[e]=t,w.messages=p.value},mergeLocaleMessage:function(e,t){p.value[e]=p.value[e]||{};const n={[e]:t};if(l)for(const r in n)d(n,r)&&Tt(n[r]);O(t=n[e],p.value[e]),w.messages=p.value},getPostTranslationHandler:function(){return E(R)?R:null},setPostTranslationHandler:function(e){R=e,w.postTranslation=e},getMissingHandler:function(){return A},setMissingHandler:function(e){null!==e&&(P=St(e)),A=e,w.missing=P},[Lt]:function(e){x=e,w.pluralRules=x}};return G.datetimeFormats=$,G.numberFormats=H,G.rt=function(...e){const[t,n,r]=e;if(r&&!k(r))throw Error(Et.INVALID_ARGUMENT);return V(t,n,u({resolvedMessage:!0},r||{}))},G.te=function(e,t){return j((()=>{if(!e)return!1;const n=Y(b(t)?t:m.value),r=w.messageResolver(n,e);return ge(r)||ft(r)||b(r)}),(()=>[e]),"translate exists",(n=>Reflect.apply(n.te,n,[e,t])),Pt,(e=>h(e)))},G.tm=function(e){const t=function(e){let t=null;const n=Ne(w,_.value,m.value);for(let r=0;r<n.length;r++){const a=p.value[n[r]]||{},o=w.messageResolver(a,e);if(null!=o){t=o;break}}return t}(e);return null!=t?t:r&&r.tm(e)||{}},G.d=function(...e){return j((t=>Reflect.apply(ze,null,[t,...e])),(()=>Qe(...e)),"datetime format",(t=>Reflect.apply(t.d,t,[...e])),(()=>we),(e=>b(e)))},G.n=function(...e){return j((t=>Reflect.apply(Ze,null,[t,...e])),(()=>tt(...e)),"number format",(t=>Reflect.apply(t.n,t,[...e])),(()=>we),(e=>b(e)))},G.getDateTimeFormat=function(e){return L.value[e]||{}},G.setDateTimeFormat=function(e,t){L.value[e]=t,w.datetimeFormats=L.value,qe(w,e,t)},G.mergeDateTimeFormat=function(e,t){L.value[e]=u(L.value[e]||{},t),w.datetimeFormats=L.value,qe(w,e,t)},G.getNumberFormat=function(e){return N.value[e]||{}},G.setNumberFormat=function(e,t){N.value[e]=t,w.numberFormats=N.value,nt(w,e,t)},G.mergeNumberFormat=function(e,t){N.value[e]=u(N.value[e]||{},t),w.numberFormats=N.value,nt(w,e,t)},G[Nt]=a,G[bt]=function(...e){return j((t=>{let n;const r=t;try{r.processor=X,n=Reflect.apply(mt,null,[r,...e])}finally{r.processor=null}return n}),(()=>dt(...e)),"translate",(t=>t[bt](...e)),(e=>[Ot(e)]),(e=>g(e)))},G[ht]=function(...e){return j((t=>Reflect.apply(ze,null,[t,...e])),(()=>Qe(...e)),"datetime format",(t=>t[ht](...e)),At,(e=>b(e)||g(e)))},G[kt]=function(...e){return j((t=>Reflect.apply(Ze,null,[t,...e])),(()=>tt(...e)),"number format",(t=>t[kt](...e)),At,(e=>b(e)||g(e)))},G}function Dt(e={}){const t=Ft(function(e){const t=b(e.locale)?e.locale:De,n=b(e.fallbackLocale)||g(e.fallbackLocale)||T(e.fallbackLocale)||!1===e.fallbackLocale?e.fallbackLocale:t,r=E(e.missing)?e.missing:void 0,a=!h(e.silentTranslationWarn)&&!c(e.silentTranslationWarn)||!e.silentTranslationWarn,o=!h(e.silentFallbackWarn)&&!c(e.silentFallbackWarn)||!e.silentFallbackWarn,s=!h(e.fallbackRoot)||e.fallbackRoot,l=!!e.formatFallbackMessages,i=T(e.modifiers)?e.modifiers:{},f=e.pluralizationRules,m=E(e.postTranslation)?e.postTranslation:void 0,_=!b(e.warnHtmlInMessage)||"off"!==e.warnHtmlInMessage,p=!!e.escapeParameterHtml,d=!h(e.sync)||e.sync;let k=e.messages;if(T(e.sharedMessages)){const t=e.sharedMessages;k=Object.keys(t).reduce(((e,n)=>{const r=e[n]||(e[n]={});return u(r,t[n]),e}),k||{})}const{__i18n:L,__root:N,__injectWithOption:y}=e,v=e.datetimeFormats,I=e.numberFormats;return{locale:t,fallbackLocale:n,messages:k,flatJson:e.flatJson,datetimeFormats:v,numberFormats:I,missing:r,missingWarn:a,fallbackWarn:o,fallbackRoot:s,fallbackFormat:l,modifiers:i,pluralRules:f,postTranslation:m,warnHtmlMessage:_,escapeParameter:p,messageResolver:e.messageResolver,inheritLocale:d,__i18n:L,__root:N,__injectWithOption:y}}(e)),{__extender:n}=e,r={id:t.id,get locale(){return t.locale.value},set locale(e){t.locale.value=e},get fallbackLocale(){return t.fallbackLocale.value},set fallbackLocale(e){t.fallbackLocale.value=e},get messages(){return t.messages.value},get datetimeFormats(){return t.datetimeFormats.value},get numberFormats(){return t.numberFormats.value},get availableLocales(){return t.availableLocales},get missing(){return t.getMissingHandler()},set missing(e){t.setMissingHandler(e)},get silentTranslationWarn(){return h(t.missingWarn)?!t.missingWarn:t.missingWarn},set silentTranslationWarn(e){t.missingWarn=h(e)?!e:e},get silentFallbackWarn(){return h(t.fallbackWarn)?!t.fallbackWarn:t.fallbackWarn},set silentFallbackWarn(e){t.fallbackWarn=h(e)?!e:e},get modifiers(){return t.modifiers},get formatFallbackMessages(){return t.fallbackFormat},set formatFallbackMessages(e){t.fallbackFormat=e},get postTranslation(){return t.getPostTranslationHandler()},set postTranslation(e){t.setPostTranslationHandler(e)},get sync(){return t.inheritLocale},set sync(e){t.inheritLocale=e},get warnHtmlInMessage(){return t.warnHtmlMessage?"warn":"off"},set warnHtmlInMessage(e){t.warnHtmlMessage="off"!==e},get escapeParameterHtml(){return t.escapeParameter},set escapeParameterHtml(e){t.escapeParameter=e},get pluralizationRules(){return t.pluralRules||{}},__composer:t,t:(...e)=>Reflect.apply(t.t,t,[...e]),rt:(...e)=>Reflect.apply(t.rt,t,[...e]),te:(e,n)=>t.te(e,n),tm:e=>t.tm(e),getLocaleMessage:e=>t.getLocaleMessage(e),setLocaleMessage(e,n){t.setLocaleMessage(e,n)},mergeLocaleMessage(e,n){t.mergeLocaleMessage(e,n)},d:(...e)=>Reflect.apply(t.d,t,[...e]),getDateTimeFormat:e=>t.getDateTimeFormat(e),setDateTimeFormat(e,n){t.setDateTimeFormat(e,n)},mergeDateTimeFormat(e,n){t.mergeDateTimeFormat(e,n)},n:(...e)=>Reflect.apply(t.n,t,[...e]),getNumberFormat:e=>t.getNumberFormat(e),setNumberFormat(e,n){t.setNumberFormat(e,n)},mergeNumberFormat(e,n){t.mergeNumberFormat(e,n)}};return r.__extender=n,r}function wt(e,t){e.locale=t.locale||e.locale,e.fallbackLocale=t.fallbackLocale||e.fallbackLocale,e.missing=t.missing||e.missing,e.silentTranslationWarn=t.silentTranslationWarn||e.silentFallbackWarn,e.silentFallbackWarn=t.silentFallbackWarn||e.silentFallbackWarn,e.formatFallbackMessages=t.formatFallbackMessages||e.formatFallbackMessages,e.postTranslation=t.postTranslation||e.postTranslation,e.warnHtmlInMessage=t.warnHtmlInMessage||e.warnHtmlInMessage,e.escapeParameterHtml=t.escapeParameterHtml||e.escapeParameterHtml,e.sync=t.sync||e.sync,e.__composer[Lt](t.pluralizationRules||e.pluralizationRules);const n=vt(e.locale,{messages:t.messages,__i18n:t.__i18n});return Object.keys(n).forEach((t=>e.mergeLocaleMessage(t,n[t]))),t.datetimeFormats&&Object.keys(t.datetimeFormats).forEach((n=>e.mergeDateTimeFormat(n,t.datetimeFormats[n]))),t.numberFormats&&Object.keys(t.numberFormats).forEach((n=>e.mergeNumberFormat(n,t.numberFormats[n]))),e}const xt={tag:{type:[String,Object]},locale:{type:String},scope:{type:String,validator:e=>"parent"===e||"global"===e,default:"parent"},i18n:{type:Object}};function Mt(){return t.Fragment}const Ut=t.defineComponent({name:"i18n-t",props:u({keypath:{type:String,required:!0},plural:{type:[Number,String],validator:e=>s(e)||!isNaN(e)}},xt),setup(e,n){const{slots:r,attrs:a}=n,o=e.i18n||Kt({useScope:e.scope,__useComponent:!0});return()=>{const s=Object.keys(r).filter((e=>"_"!==e)),l=m();e.locale&&(l.locale=e.locale),void 0!==e.plural&&(l.plural=b(e.plural)?+e.plural:e.plural);const c=function({slots:e},n){if(1===n.length&&"default"===n[0])return(e.default?e.default():[]).reduce(((e,n)=>[...e,...n.type===t.Fragment?n.children:[n]]),[]);return n.reduce(((t,n)=>{const r=e[n];return r&&(t[n]=r()),t}),m())}(n,s),i=o[bt](e.keypath,c,l),f=u(m(),a),_=b(e.tag)||k(e.tag)?e.tag:Mt();return t.h(_,f,i)}}}),Wt=Ut;function $t(e,n,r,a){const{slots:o,attrs:s}=n;return()=>{const n={part:!0};let l=m();e.locale&&(n.locale=e.locale),b(e.format)?n.key=e.format:k(e.format)&&(b(e.format.key)&&(n.key=e.format.key),l=Object.keys(e.format).reduce(((t,n)=>r.includes(n)?u(m(),t,{[n]:e.format[n]}):t),m()));const c=a(e.value,n,l);let i=[n.key];g(c)?i=c.map(((e,t)=>{const n=o[e.type],r=n?n({[e.type]:e.value,index:t,parts:c}):[e.value];var a;return g(a=r)&&!b(a[0])&&(r[0].key=`${e.type}-${t}`),r})):b(c)&&(i=[c]);const f=u(m(),s),_=b(e.tag)||k(e.tag)?e.tag:Mt();return t.h(_,f,i)}}const Ht=t.defineComponent({name:"i18n-n",props:u({value:{type:Number,required:!0},format:{type:[String,Object]}},xt),setup(e,t){const n=e.i18n||Kt({useScope:e.scope,__useComponent:!0});return $t(e,t,et,((...e)=>n[kt](...e)))}}),jt=Ht;function Vt(e){const r=t=>{const{instance:n,value:r}=t;if(!n||!n.$)throw Error(Et.UNEXPECTED_ERROR);const a=function(e,t){const n=e;if("composition"===e.mode)return n.__getInstance(t)||e.global;{const r=n.__getInstance(t);return null!=r?r.__composer:e.global.__composer}}(e,n.$),o=Xt(r);return[Reflect.apply(a.t,a,[...Yt(o)]),a]};return{created:(a,o)=>{const[s,l]=r(o);n&&e.global===l&&(a.__i18nWatcher=t.watch(l.locale,(()=>{o.instance&&o.instance.$forceUpdate()}))),a.__composer=l,a.textContent=s},unmounted:e=>{n&&e.__i18nWatcher&&(e.__i18nWatcher(),e.__i18nWatcher=void 0,delete e.__i18nWatcher),e.__composer&&(e.__composer=void 0,delete e.__composer)},beforeUpdate:(e,{value:t})=>{if(e.__composer){const n=e.__composer,r=Xt(t);e.textContent=Reflect.apply(n.t,n,[...Yt(r)])}},getSSRProps:e=>{const[t]=r(e);return{textContent:t}}}}function Xt(e){if(b(e))return{path:e};if(T(e)){if(!("path"in e))throw Error(Et.REQUIRED_VALUE,"path");return e}throw Error(Et.INVALID_VALUE)}function Yt(e){const{path:t,locale:n,args:r,choice:a,plural:o}=e,l={},c=r||{};return b(n)&&(l.locale=n),s(a)&&(l.plural=a),s(o)&&(l.plural=o),[t,c,l]}const Gt=r("global-vue-i18n");function Kt(e={}){const n=t.getCurrentInstance();if(null==n)throw Error(Et.MUST_BE_CALL_SETUP_TOP);if(!n.isCE&&null!=n.appContext.app&&!n.appContext.app.__VUE_I18N_SYMBOL__)throw Error(Et.NOT_INSTALLED);const r=function(e){const n=t.inject(e.isCE?Gt:e.appContext.app.__VUE_I18N_SYMBOL__);if(!n)throw Error(e.isCE?Et.NOT_INSTALLED_WITH_PROVIDE:Et.UNEXPECTED_ERROR);return n}(n),a=function(e){return"composition"===e.mode?e.global:e.global.__composer}(r),o=It(n),s=function(e,t){return i(e)?"__i18n"in t?"local":"global":e.useScope?e.useScope:"local"}(e,o);if("global"===s)return Ct(a,e,o),a;if("parent"===s){let t=function(e,t,n=!1){let r=null;const a=t.root;let o=function(e,t=!1){if(null==e)return null;return t&&e.vnode.ctx||e.parent}(t,n);for(;null!=o;){const t=e;if("composition"===e.mode)r=t.__getInstance(o);else{const e=t.__getInstance(o);null!=e&&(r=e.__composer,n&&r&&!r[Nt]&&(r=null))}if(null!=r)break;if(a===o)break;o=o.parent}return r}(r,n,e.__useComponent);return null==t&&(t=a),t}const l=r;let c=l.__getInstance(n);if(null==c){const r=u({},e);"__i18n"in o&&(r.__i18n=o.__i18n),a&&(r.__root=a),c=Ft(r),l.__composerExtend&&(c[yt]=l.__composerExtend(c)),function(e,n,r){t.onMounted((()=>{}),n),t.onUnmounted((()=>{const t=r;e.__deleteInstance(n);const a=t[yt];a&&(a(),delete t[yt])}),n)}(l,n,c),l.__setInstance(n,c)}return c}const Bt=["locale","fallbackLocale","availableLocales"],zt=["t","rt","d","n","tm","te"];const Jt=t.defineComponent({name:"i18n-d",props:u({value:{type:[Number,Date],required:!0},format:{type:[String,Object]}},xt),setup(e,t){const n=e.i18n||Kt({useScope:e.scope,__useComponent:!0});return $t(e,t,Je,((...e)=>n[ht](...e)))}}),Qt=Jt;return Me=function(e,t){if(b(e)){!h(t.warnHtmlMessage)||t.warnHtmlMessage;const n=(t.onCacheKey||pe)(e),r=de[n];if(r)return r;const{ast:a,detectError:o}=function(e,t={}){let n=!1;const r=t.onError||R;return t.onError=e=>{n=!0,r(e)},{...q(e,t),detectError:n}}(e,{...t,location:!1,jit:!0}),s=Z(a);return o?s:de[n]=s}{const t=e.cacheKey;if(t){const n=de[t];return n||(de[t]=Z(e))}return Z(e)}},Ue=function(e,t){if(!k(e))return null;let n=Pe.get(t);if(n||(n=function(e){const t=[];let n,r,a,o,s,l,c,i=-1,u=0,f=0;const m=[];function _(){const t=e[i+1];if(5===u&&"'"===t||6===u&&'"'===t)return i++,a="\\"+t,m[0](),!0}for(m[0]=()=>{void 0===r?r=a:r+=a},m[1]=()=>{void 0!==r&&(t.push(r),r=void 0)},m[2]=()=>{m[0](),f++},m[3]=()=>{if(f>0)f--,u=4,m[0]();else{if(f=0,void 0===r)return!1;if(r=Ae(r),!1===r)return!1;m[1]()}};null!==u;)if(i++,n=e[i],"\\"!==n||!_()){if(o=Oe(n),c=Ie[u],s=c[o]||c.l||8,8===s)return;if(u=s[0],void 0!==s[1]&&(l=m[s[1]],l&&(a=n,!1===l())))return;if(7===u)return t}}(t),n&&Pe.set(t,n)),!n)return null;const r=n.length;let a=e,o=0;for(;o<r;){const e=a[n[o]];if(void 0===e)return null;if(E(a))return null;a=e,o++}return a},We=Ne,e.DatetimeFormat=Jt,e.I18nD=Qt,e.I18nInjectionKey=Gt,e.I18nN=jt,e.I18nT=Wt,e.NumberFormat=Ht,e.Translation=Ut,e.VERSION=gt,e.createI18n=function(e={}){const n=!h(e.legacy)||e.legacy,a=!h(e.globalInjection)||e.globalInjection,o=new Map,[s,l]=function(e,n){const r=t.effectScope(),a=n?r.run((()=>Dt(e))):r.run((()=>Ft(e)));if(null==a)throw Error(Et.UNEXPECTED_ERROR);return[r,a]}(e,n),c=r(""),i={get mode(){return n?"legacy":"composition"},async install(e,...r){if(e.__VUE_I18N_SYMBOL__=c,e.provide(e.__VUE_I18N_SYMBOL__,i),T(r[0])){const e=r[0];i.__composerExtend=e.__composerExtend,i.__vueI18nExtend=e.__vueI18nExtend}let o=null;!n&&a&&(o=function(e,n){const r=Object.create(null);Bt.forEach((e=>{const a=Object.getOwnPropertyDescriptor(n,e);if(!a)throw Error(Et.UNEXPECTED_ERROR);const o=t.isRef(a.value)?{get:()=>a.value.value,set(e){a.value.value=e}}:{get:()=>a.get&&a.get()};Object.defineProperty(r,e,o)})),e.config.globalProperties.$i18n=r,zt.forEach((t=>{const r=Object.getOwnPropertyDescriptor(n,t);if(!r||!r.value)throw Error(Et.UNEXPECTED_ERROR);Object.defineProperty(e.config.globalProperties,`$${t}`,r)}));const a=()=>{delete e.config.globalProperties.$i18n,zt.forEach((t=>{delete e.config.globalProperties[`$${t}`]}))};return a}(e,i.global)),function(e,t,...n){const r=T(n[0])?n[0]:{};(!h(r.globalInstall)||r.globalInstall)&&([Ut.name,"I18nT"].forEach((t=>e.component(t,Ut))),[Ht.name,"I18nN"].forEach((t=>e.component(t,Ht))),[Jt.name,"I18nD"].forEach((t=>e.component(t,Jt)))),e.directive("t",Vt(t))}(e,i,...r),n&&e.mixin(function(e,n,r){return{beforeCreate(){const a=t.getCurrentInstance();if(!a)throw Error(Et.UNEXPECTED_ERROR);const o=this.$options;if(o.i18n){const t=o.i18n;if(o.__i18n&&(t.__i18n=o.__i18n),t.__root=n,this===this.$root)this.$i18n=wt(e,t);else{t.__injectWithOption=!0,t.__extender=r.__vueI18nExtend,this.$i18n=Dt(t);const e=this.$i18n;e.__extender&&(e.__disposer=e.__extender(this.$i18n))}}else if(o.__i18n)if(this===this.$root)this.$i18n=wt(e,o);else{this.$i18n=Dt({__i18n:o.__i18n,__injectWithOption:!0,__extender:r.__vueI18nExtend,__root:n});const e=this.$i18n;e.__extender&&(e.__disposer=e.__extender(this.$i18n))}else this.$i18n=e;o.__i18nGlobal&&Ct(n,o,o),this.$t=(...e)=>this.$i18n.t(...e),this.$rt=(...e)=>this.$i18n.rt(...e),this.$te=(e,t)=>this.$i18n.te(e,t),this.$d=(...e)=>this.$i18n.d(...e),this.$n=(...e)=>this.$i18n.n(...e),this.$tm=e=>this.$i18n.tm(e),r.__setInstance(a,this.$i18n)},mounted(){},unmounted(){const e=t.getCurrentInstance();if(!e)throw Error(Et.UNEXPECTED_ERROR);const n=this.$i18n;delete this.$t,delete this.$rt,delete this.$te,delete this.$d,delete this.$n,delete this.$tm,n.__disposer&&(n.__disposer(),delete n.__disposer,delete n.__extender),r.__deleteInstance(e),delete this.$i18n}}}(l,l.__composer,i));const s=e.unmount;e.unmount=()=>{o&&o(),i.dispose(),s()}},get global(){return l},dispose(){s.stop()},__instances:o,__getInstance:function(e){return o.get(e)||null},__setInstance:function(e,t){o.set(e,t)},__deleteInstance:function(e){o.delete(e)}};return i},e.useI18n=Kt,e.vTDirective=Vt,e}({},Vue);
