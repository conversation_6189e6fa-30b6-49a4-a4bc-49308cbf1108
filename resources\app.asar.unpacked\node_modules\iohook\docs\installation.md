# Installation

iohook provides prebuilt versions for a bunch of OSes and runtime versions.

```bash
npm install iohook --save # or yarn add iohook
```

iohook currently provides prebuilt versions for the following runtimes:

- Electron:

  - 4.0.4 (ABI 69)
  - 5.X.X (ABI 70)
  - 6.X.X (ABI 73)
  - 7.X.X (ABI 75)
  - 8.X.X (ABI 76)
  - 9.X.X (ABI 80)
  - 10.X.X (ABI 82)
  - 11.X.X (ABI 85)
  - 12.X.X (ABI 87)

- Node.js:
  - 8.9.X (ABI 57)
  - 9.2.X (ABI 59)
  - 10.X.X (ABI 64)
  - 11.X.X (ABI 67)
  - 12.X.X (ABI 72)
  - 13.X.X (ABI 79)
  - 14.X.X (ABI 83)
  - 15.X.X (ABI 88)
