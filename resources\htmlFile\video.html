<!DOCTYPE html>
<html lang="zh">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>播放视频</title>
</head>
<style>
    * {
        margin: 0;
        padding: 0;
    }

    .loda {
        position: relative;
        width: 100vw;
        height: 100vh;
        overflow: hidden;
    }



    .video,
    .video1 {
        object-fit: cover;
        width: 100%;
        height: 100vh;
        position: absolute;
        overflow: hidden;

    }

    .iframe {
        object-fit: cover;
        width: 100%;
        height: 100vh;
        position: absolute;
        overflow: hidden;
        cursor: none;
        z-index: 1;
    }

    .Clock {
        width: 640px;
        height: 360px;
        position: absolute;
        z-index: +2;
        /* top: 50%;
        left: 50%;
        transform: translate(-50%, -50%); */
        overflow: hidden;
        /* background-color: red; */
    }

    .Clock_iframe {
        width: 100%;
        height: 100%;
        overflow: hidden;
    }
</style>

<body>
    <!-- 13 21 去除黑色背景  -->
    <div class="loda">
        <div class="Clock">
            <iframe src="" class="Clock_iframe" frameborder="0"></iframe>
        </div>
        <!-- muted="true" -->
        <video autoplay loop class="video">
            您的浏览器不支持视频标签。
        </video>
        <video style="display: none;" autoplay loop class="video1">
            您的浏览器不支持视频标签。
        </video>
        <iframe frameborder="0" class="iframe"></iframe>
    </div>
    <script>
        let curPlay = true
        const videoPlayer = document.querySelector('.video');
        const Clock = document.querySelector('.Clock');
        const videoPlayer1 = document.querySelector('.video1');
        const iframe = document.querySelector('.iframe');
        const ClockIframe = document.querySelector('.Clock_iframe');
        videoPlayer.muted = true
        videoPlayer1.muted = true
        document.addEventListener('DOMContentLoaded', () => {
            // 获取 URL 参数（此处代码未使用，但保留以备将来需要）
            const urlParams = new URLSearchParams(window.location.search);

            const videoSrc = urlParams.get('src');
            const type = urlParams.get('type');
            if (!urlParams) return
            // 视频源路径（此处为硬编码路径，也可以从 URL 参数中获取）

            if (type == 1) {
                // 设置视频源并加载视频
                if (videoPlayer && videoSrc) {
                    videoPlayer.src = videoSrc;
                    videoPlayer.load(); // 通常不需要显式调用 load()，因为设置 src 后浏览器会自动加载
                }
            } else if (type == 6) {
                if (iframe && videoSrc) {
                    iframe.src = videoSrc;
                } 
            }

        });
        // 使用暴露的API来监听来自主进程的消息
        window.electronAPI.on('message-from-main', (arg) => {
            switch (arg.type) {
                case 1:
                    iframe.src = '';
                    curPlay = !curPlay
                    if (curPlay) {
                        videoPlayer.src = arg.path;
                        videoPlayer.addEventListener('loadeddata', () => {
                            videoPlayer.style.display = 'block';
                            videoPlayer1.style.display = 'none';
                            videoPlayer1.src = ''
                        });
                    } else {
                        videoPlayer1.src = arg.path;
                        videoPlayer1.addEventListener('loadeddata', () => {
                            videoPlayer1.style.display = 'block';
                            videoPlayer.style.display = 'none';
                            videoPlayer.src = ''
                        });
                    }
                    break;
                case 2:
                    console.log('暂停');
                    
                    videoPlayer.pause()
                    videoPlayer1.pause()
                    break;
                case 3:
                console.log('播放');
                    videoPlayer.play()
                    videoPlayer1.play()
                    break;
                case 4:
                    videoPlayer.muted = true
                    videoPlayer1.muted = true
                    break;
                case 5:
                    videoPlayer.muted = false
                    videoPlayer1.muted = false
                    break;
                case 6:
                    iframe.src = arg.path;
                    iframe.style.display = 'block';
                    break;
                case 7:
                    ClockIframe.src = arg.path;
                    break;
            }
        });
        //设置时钟位置
        window.electronAPI.on("setClock", (i) => {

            switch (i) {
                case 0:
                    Clock.style.left = '0%'
                    Clock.style.top = '0%'
                    Clock.style.bottom = ''
                    Clock.style.right = ''
                    Clock.style.transform = "translate(-0%, -0%)"
                    break
                case 1:
                    Clock.style.left = '50%'
                    Clock.style.top = '0%'
                    Clock.style.bottom = ''
                    Clock.style.right = ''
                    Clock.style.transform = "translate(-50%, -0%)"
                    break
                case 2:
                    Clock.style.right = '0%'
                    Clock.style.top = '0%'
                    Clock.style.bottom = ''
                    Clock.style.left = ''
                    Clock.style.transform = "translate(-0%, -0%)"
                    break
                case 3:
                    Clock.style.left = '0%'
                    Clock.style.top = '50%'
                    Clock.style.bottom = ''
                    Clock.style.right = ''
                    Clock.style.transform = "translate(-0%, -50%)"
                    break
                case 4:
                    Clock.style.left = '50%'
                    Clock.style.top = '50%'
                    Clock.style.bottom = ''
                    Clock.style.right = ''
                    Clock.style.transform = "translate(-50%, -50%)"
                    break
                case 5:
                    Clock.style.right = '0%'
                    Clock.style.left = ''
                    Clock.style.top = '50%'
                    Clock.style.bottom = ''
                    Clock.style.transform = "translate(-0%, -50%)"
                    break
                case 6:
                    Clock.style.left = '0%'
                    Clock.style.bottom = '0'
                    Clock.style.top = ''
                    Clock.style.right = ''
                    Clock.style.transform = "translate(-0%, -0%)"
                    break
                case 7:
                    Clock.style.left = '50%'
                    Clock.style.bottom = '0'
                    Clock.style.top = ''
                    Clock.style.right = ''
                    Clock.style.transform = "translate(-50%, -0%)"
                    break
                case 8:
                    Clock.style.right = '0'
                    Clock.style.bottom = '0'
                    Clock.style.top = ''
                    Clock.style.left = ''
                    Clock.style.transform = "translate(-0%, -0%)"
                    break
                case 10:
                    Clock.style.display = 'block'
                    break
                case 11:
                    Clock.style.display = 'none'
                    break

            }

        })

    </script>
</body>

</html>