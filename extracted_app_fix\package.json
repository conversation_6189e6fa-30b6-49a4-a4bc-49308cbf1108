{"name": "WallpaperPlay", "private": true, "version": "1.1.1", "main": "dist/electron/index.js", "dependencies": {"@electron/asar": "^3.3.1", "@element-plus/icons-vue": "^2.3.1", "adm-zip": "^0.5.16", "async": "^3.2.6", "asynckit": "^0.4.0", "axios": "^1.7.9", "bindings": "^1.5.0", "call-bind-apply-helpers": "^1.0.2", "combined-stream": "^1.0.8", "cross-env": "^7.0.3", "delayed-stream": "^1.0.0", "dunder-proto": "^1.0.1", "electron-as-wallpaper": "^2.0.3", "element-plus": "^2.9.0", "es-define-property": "^1.0.1", "es-errors": "^1.3.0", "es-object-atoms": "^1.1.1", "es-set-tostringtag": "^2.1.0", "esbuild": "^0.22.0", "ffmpeg": "^0.0.4", "ffmpeg-static": "^5.2.0", "file-uri-to-path": "^2.0.0", "fluent-ffmpeg": "^2.1.3", "follow-redirects": "^1.15.9", "form-data": "^4.0.2", "fs-extra": "^11.3.0", "function-bind": "^1.1.2", "get-intrinsic": "^1.3.0", "get-proto": "^1.0.1", "glob": "^11.0.1", "gopd": "^1.2.0", "has-symbols": "^1.1.0", "has-tostringtag": "^1.0.2", "hasown": "^2.0.2", "iohook": "0.12.0", "isexe": "^2.0.0", "jszip": "^3.10.1", "lru-cache": "^11.0.2", "math-intrinsics": "^1.1.0", "mime-db": "^1.54.0", "mime-types": "^2.1.35", "minimatch": "^10.0.1", "minipass": "^7.1.2", "pako": "^2.1.0", "path-scurry": "^2.0.0", "pinia": "^2.3.0", "proxy-from-env": "^1.1.0", "qrcode": "^1.5.3", "setimmediate": "^1.0.5", "vue": "^3.2.47", "vue-i18n": "^11.0.1", "vue-router": "^4.5.0", "which": "^5.0.0"}}