{"version": 3, "file": "index.js", "sources": ["../../../../../packages/components/tree-v2/index.ts"], "sourcesContent": ["import { withInstall } from '@element-plus/utils'\nimport TreeV2 from './src/tree.vue'\nimport type { SFCWithInstall } from '@element-plus/utils'\n\nexport const ElTreeV2: SFCWithInstall<typeof TreeV2> = withInstall(TreeV2)\nexport default ElTreeV2\n"], "names": ["withInstall", "TreeV2"], "mappings": ";;;;;;;AAEY,MAAC,QAAQ,GAAGA,mBAAW,CAACC,eAAM;;;;;"}