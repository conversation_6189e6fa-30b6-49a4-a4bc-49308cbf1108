{"version": 3, "file": "index.js", "sources": ["../../../../../packages/components/table/index.ts"], "sourcesContent": ["import { withInstall, withNoopInstall } from '@element-plus/utils'\nimport Table from './src/table.vue'\nimport TableColumn from './src/tableColumn'\nimport type { SFCWithInstall } from '@element-plus/utils'\n\nexport const ElTable: SFCWithInstall<typeof Table> & {\n  TableColumn: typeof TableColumn\n} = withInstall(Table, {\n  TableColumn,\n})\nexport default ElTable\nexport const ElTableColumn: SFCWithInstall<typeof TableColumn> =\n  withNoopInstall(TableColumn)\n\nexport type TableInstance = InstanceType<typeof Table> & unknown\n\nexport type TableColumnInstance = InstanceType<typeof TableColumn> & unknown\n\nexport type {\n  SummaryMethod,\n  Table,\n  TableProps,\n  TableRefs,\n  ColumnCls,\n  ColumnStyle,\n  CellCls,\n  CellStyle,\n  TreeNode,\n  RenderRowData,\n  Sort,\n  Filter,\n  TableColumnCtx,\n  TableTooltipData,\n} from './src/table/defaults'\n"], "names": ["withInstall", "Table", "TableColumn", "withNoopInstall"], "mappings": ";;;;;;;;AAGY,MAAC,OAAO,GAAGA,mBAAW,CAACC,gBAAK,EAAE;AAC1C,eAAEC,gBAAW;AACb,CAAC,EAAE;AAES,MAAC,aAAa,GAAGC,uBAAe,CAACD,gBAAW;;;;;;"}