/*! pako 2.1.0 https://github.com/nodeca/pako @license (MIT AND Zlib) */
!function(t,e){"object"==typeof exports&&"undefined"!=typeof module?e(exports):"function"==typeof define&&define.amd?define(["exports"],e):e((t="undefined"!=typeof globalThis?globalThis:t||self).pako={})}(this,(function(t){"use strict";function e(t){for(var e=t.length;--e>=0;)t[e]=0}var a=256,r=286,n=30,i=15,s=new Uint8Array([0,0,0,0,0,0,0,0,1,1,1,1,2,2,2,2,3,3,3,3,4,4,4,4,5,5,5,5,0]),_=new Uint8Array([0,0,0,0,1,1,2,2,3,3,4,4,5,5,6,6,7,7,8,8,9,9,10,10,11,11,12,12,13,13]),h=new Uint8Array([0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,2,3,7]),o=new Uint8Array([16,17,18,0,8,7,9,6,10,5,11,4,12,3,13,2,14,1,15]),l=new Array(576);e(l);var d=new Array(60);e(d);var u=new Array(512);e(u);var f=new Array(256);e(f);var c=new Array(29);e(c);var p,g,w,m=new Array(n);function b(t,e,a,r,n){this.static_tree=t,this.extra_bits=e,this.extra_base=a,this.elems=r,this.max_length=n,this.has_stree=t&&t.length}function v(t,e){this.dyn_tree=t,this.max_code=0,this.stat_desc=e}e(m);var y=function(t){return t<256?u[t]:u[256+(t>>>7)]},z=function(t,e){t.pending_buf[t.pending++]=255&e,t.pending_buf[t.pending++]=e>>>8&255},k=function(t,e,a){t.bi_valid>16-a?(t.bi_buf|=e<<t.bi_valid&65535,z(t,t.bi_buf),t.bi_buf=e>>16-t.bi_valid,t.bi_valid+=a-16):(t.bi_buf|=e<<t.bi_valid&65535,t.bi_valid+=a)},x=function(t,e,a){k(t,a[2*e],a[2*e+1])},A=function(t,e){var a=0;do{a|=1&t,t>>>=1,a<<=1}while(--e>0);return a>>>1},E=function(t,e,a){var r,n,s=new Array(16),_=0;for(r=1;r<=i;r++)_=_+a[r-1]<<1,s[r]=_;for(n=0;n<=e;n++){var h=t[2*n+1];0!==h&&(t[2*n]=A(s[h]++,h))}},Z=function(t){var e;for(e=0;e<r;e++)t.dyn_ltree[2*e]=0;for(e=0;e<n;e++)t.dyn_dtree[2*e]=0;for(e=0;e<19;e++)t.bl_tree[2*e]=0;t.dyn_ltree[512]=1,t.opt_len=t.static_len=0,t.sym_next=t.matches=0},S=function(t){t.bi_valid>8?z(t,t.bi_buf):t.bi_valid>0&&(t.pending_buf[t.pending++]=t.bi_buf),t.bi_buf=0,t.bi_valid=0},U=function(t,e,a,r){var n=2*e,i=2*a;return t[n]<t[i]||t[n]===t[i]&&r[e]<=r[a]},R=function(t,e,a){for(var r=t.heap[a],n=a<<1;n<=t.heap_len&&(n<t.heap_len&&U(e,t.heap[n+1],t.heap[n],t.depth)&&n++,!U(e,r,t.heap[n],t.depth));)t.heap[a]=t.heap[n],a=n,n<<=1;t.heap[a]=r},T=function(t,e,r){var n,i,h,o,l=0;if(0!==t.sym_next)do{n=255&t.pending_buf[t.sym_buf+l++],n+=(255&t.pending_buf[t.sym_buf+l++])<<8,i=t.pending_buf[t.sym_buf+l++],0===n?x(t,i,e):(h=f[i],x(t,h+a+1,e),0!==(o=s[h])&&(i-=c[h],k(t,i,o)),n--,h=y(n),x(t,h,r),0!==(o=_[h])&&(n-=m[h],k(t,n,o)))}while(l<t.sym_next);x(t,256,e)},L=function(t,e){var a,r,n,s=e.dyn_tree,_=e.stat_desc.static_tree,h=e.stat_desc.has_stree,o=e.stat_desc.elems,l=-1;for(t.heap_len=0,t.heap_max=573,a=0;a<o;a++)0!==s[2*a]?(t.heap[++t.heap_len]=l=a,t.depth[a]=0):s[2*a+1]=0;for(;t.heap_len<2;)s[2*(n=t.heap[++t.heap_len]=l<2?++l:0)]=1,t.depth[n]=0,t.opt_len--,h&&(t.static_len-=_[2*n+1]);for(e.max_code=l,a=t.heap_len>>1;a>=1;a--)R(t,s,a);n=o;do{a=t.heap[1],t.heap[1]=t.heap[t.heap_len--],R(t,s,1),r=t.heap[1],t.heap[--t.heap_max]=a,t.heap[--t.heap_max]=r,s[2*n]=s[2*a]+s[2*r],t.depth[n]=(t.depth[a]>=t.depth[r]?t.depth[a]:t.depth[r])+1,s[2*a+1]=s[2*r+1]=n,t.heap[1]=n++,R(t,s,1)}while(t.heap_len>=2);t.heap[--t.heap_max]=t.heap[1],function(t,e){var a,r,n,s,_,h,o=e.dyn_tree,l=e.max_code,d=e.stat_desc.static_tree,u=e.stat_desc.has_stree,f=e.stat_desc.extra_bits,c=e.stat_desc.extra_base,p=e.stat_desc.max_length,g=0;for(s=0;s<=i;s++)t.bl_count[s]=0;for(o[2*t.heap[t.heap_max]+1]=0,a=t.heap_max+1;a<573;a++)(s=o[2*o[2*(r=t.heap[a])+1]+1]+1)>p&&(s=p,g++),o[2*r+1]=s,r>l||(t.bl_count[s]++,_=0,r>=c&&(_=f[r-c]),h=o[2*r],t.opt_len+=h*(s+_),u&&(t.static_len+=h*(d[2*r+1]+_)));if(0!==g){do{for(s=p-1;0===t.bl_count[s];)s--;t.bl_count[s]--,t.bl_count[s+1]+=2,t.bl_count[p]--,g-=2}while(g>0);for(s=p;0!==s;s--)for(r=t.bl_count[s];0!==r;)(n=t.heap[--a])>l||(o[2*n+1]!==s&&(t.opt_len+=(s-o[2*n+1])*o[2*n],o[2*n+1]=s),r--)}}(t,e),E(s,l,t.bl_count)},F=function(t,e,a){var r,n,i=-1,s=e[1],_=0,h=7,o=4;for(0===s&&(h=138,o=3),e[2*(a+1)+1]=65535,r=0;r<=a;r++)n=s,s=e[2*(r+1)+1],++_<h&&n===s||(_<o?t.bl_tree[2*n]+=_:0!==n?(n!==i&&t.bl_tree[2*n]++,t.bl_tree[32]++):_<=10?t.bl_tree[34]++:t.bl_tree[36]++,_=0,i=n,0===s?(h=138,o=3):n===s?(h=6,o=3):(h=7,o=4))},O=function(t,e,a){var r,n,i=-1,s=e[1],_=0,h=7,o=4;for(0===s&&(h=138,o=3),r=0;r<=a;r++)if(n=s,s=e[2*(r+1)+1],!(++_<h&&n===s)){if(_<o)do{x(t,n,t.bl_tree)}while(0!=--_);else 0!==n?(n!==i&&(x(t,n,t.bl_tree),_--),x(t,16,t.bl_tree),k(t,_-3,2)):_<=10?(x(t,17,t.bl_tree),k(t,_-3,3)):(x(t,18,t.bl_tree),k(t,_-11,7));_=0,i=n,0===s?(h=138,o=3):n===s?(h=6,o=3):(h=7,o=4)}},D=!1,N=function(t,e,a,r){k(t,0+(r?1:0),3),S(t),z(t,a),z(t,~a),a&&t.pending_buf.set(t.window.subarray(e,e+a),t.pending),t.pending+=a},I=function(t,e,r,n){var i,s,_=0;t.level>0?(2===t.strm.data_type&&(t.strm.data_type=function(t){var e,r=4093624447;for(e=0;e<=31;e++,r>>>=1)if(1&r&&0!==t.dyn_ltree[2*e])return 0;if(0!==t.dyn_ltree[18]||0!==t.dyn_ltree[20]||0!==t.dyn_ltree[26])return 1;for(e=32;e<a;e++)if(0!==t.dyn_ltree[2*e])return 1;return 0}(t)),L(t,t.l_desc),L(t,t.d_desc),_=function(t){var e;for(F(t,t.dyn_ltree,t.l_desc.max_code),F(t,t.dyn_dtree,t.d_desc.max_code),L(t,t.bl_desc),e=18;e>=3&&0===t.bl_tree[2*o[e]+1];e--);return t.opt_len+=3*(e+1)+5+5+4,e}(t),i=t.opt_len+3+7>>>3,(s=t.static_len+3+7>>>3)<=i&&(i=s)):i=s=r+5,r+4<=i&&-1!==e?N(t,e,r,n):4===t.strategy||s===i?(k(t,2+(n?1:0),3),T(t,l,d)):(k(t,4+(n?1:0),3),function(t,e,a,r){var n;for(k(t,e-257,5),k(t,a-1,5),k(t,r-4,4),n=0;n<r;n++)k(t,t.bl_tree[2*o[n]+1],3);O(t,t.dyn_ltree,e-1),O(t,t.dyn_dtree,a-1)}(t,t.l_desc.max_code+1,t.d_desc.max_code+1,_+1),T(t,t.dyn_ltree,t.dyn_dtree)),Z(t),n&&S(t)},C={_tr_init:function(t){D||(!function(){var t,e,a,o,v,y=new Array(16);for(a=0,o=0;o<28;o++)for(c[o]=a,t=0;t<1<<s[o];t++)f[a++]=o;for(f[a-1]=o,v=0,o=0;o<16;o++)for(m[o]=v,t=0;t<1<<_[o];t++)u[v++]=o;for(v>>=7;o<n;o++)for(m[o]=v<<7,t=0;t<1<<_[o]-7;t++)u[256+v++]=o;for(e=0;e<=i;e++)y[e]=0;for(t=0;t<=143;)l[2*t+1]=8,t++,y[8]++;for(;t<=255;)l[2*t+1]=9,t++,y[9]++;for(;t<=279;)l[2*t+1]=7,t++,y[7]++;for(;t<=287;)l[2*t+1]=8,t++,y[8]++;for(E(l,287,y),t=0;t<n;t++)d[2*t+1]=5,d[2*t]=A(t,5);p=new b(l,s,257,r,i),g=new b(d,_,0,n,i),w=new b(new Array(0),h,0,19,7)}(),D=!0),t.l_desc=new v(t.dyn_ltree,p),t.d_desc=new v(t.dyn_dtree,g),t.bl_desc=new v(t.bl_tree,w),t.bi_buf=0,t.bi_valid=0,Z(t)},_tr_stored_block:N,_tr_flush_block:I,_tr_tally:function(t,e,r){return t.pending_buf[t.sym_buf+t.sym_next++]=e,t.pending_buf[t.sym_buf+t.sym_next++]=e>>8,t.pending_buf[t.sym_buf+t.sym_next++]=r,0===e?t.dyn_ltree[2*r]++:(t.matches++,e--,t.dyn_ltree[2*(f[r]+a+1)]++,t.dyn_dtree[2*y(e)]++),t.sym_next===t.sym_end},_tr_align:function(t){k(t,2,3),x(t,256,l),function(t){16===t.bi_valid?(z(t,t.bi_buf),t.bi_buf=0,t.bi_valid=0):t.bi_valid>=8&&(t.pending_buf[t.pending++]=255&t.bi_buf,t.bi_buf>>=8,t.bi_valid-=8)}(t)}},B=function(t,e,a,r){for(var n=65535&t|0,i=t>>>16&65535|0,s=0;0!==a;){a-=s=a>2e3?2e3:a;do{i=i+(n=n+e[r++]|0)|0}while(--s);n%=65521,i%=65521}return n|i<<16|0},H=new Uint32Array(function(){for(var t,e=[],a=0;a<256;a++){t=a;for(var r=0;r<8;r++)t=1&t?3988292384^t>>>1:t>>>1;e[a]=t}return e}()),M=function(t,e,a,r){var n=H,i=r+a;t^=-1;for(var s=r;s<i;s++)t=t>>>8^n[255&(t^e[s])];return-1^t},P={2:"need dictionary",1:"stream end",0:"","-1":"file error","-2":"stream error","-3":"data error","-4":"insufficient memory","-5":"buffer error","-6":"incompatible version"},j={Z_NO_FLUSH:0,Z_PARTIAL_FLUSH:1,Z_SYNC_FLUSH:2,Z_FULL_FLUSH:3,Z_FINISH:4,Z_BLOCK:5,Z_TREES:6,Z_OK:0,Z_STREAM_END:1,Z_NEED_DICT:2,Z_ERRNO:-1,Z_STREAM_ERROR:-2,Z_DATA_ERROR:-3,Z_MEM_ERROR:-4,Z_BUF_ERROR:-5,Z_NO_COMPRESSION:0,Z_BEST_SPEED:1,Z_BEST_COMPRESSION:9,Z_DEFAULT_COMPRESSION:-1,Z_FILTERED:1,Z_HUFFMAN_ONLY:2,Z_RLE:3,Z_FIXED:4,Z_DEFAULT_STRATEGY:0,Z_BINARY:0,Z_TEXT:1,Z_UNKNOWN:2,Z_DEFLATED:8},K=C._tr_init,Y=C._tr_stored_block,G=C._tr_flush_block,X=C._tr_tally,W=C._tr_align,q=j.Z_NO_FLUSH,J=j.Z_PARTIAL_FLUSH,Q=j.Z_FULL_FLUSH,V=j.Z_FINISH,$=j.Z_BLOCK,tt=j.Z_OK,et=j.Z_STREAM_END,at=j.Z_STREAM_ERROR,rt=j.Z_DATA_ERROR,nt=j.Z_BUF_ERROR,it=j.Z_DEFAULT_COMPRESSION,st=j.Z_FILTERED,_t=j.Z_HUFFMAN_ONLY,ht=j.Z_RLE,ot=j.Z_FIXED,lt=j.Z_DEFAULT_STRATEGY,dt=j.Z_UNKNOWN,ut=j.Z_DEFLATED,ft=258,ct=262,pt=42,gt=113,wt=666,mt=function(t,e){return t.msg=P[e],e},bt=function(t){return 2*t-(t>4?9:0)},vt=function(t){for(var e=t.length;--e>=0;)t[e]=0},yt=function(t){var e,a,r,n=t.w_size;r=e=t.hash_size;do{a=t.head[--r],t.head[r]=a>=n?a-n:0}while(--e);r=e=n;do{a=t.prev[--r],t.prev[r]=a>=n?a-n:0}while(--e)},zt=function(t,e,a){return(e<<t.hash_shift^a)&t.hash_mask},kt=function(t){var e=t.state,a=e.pending;a>t.avail_out&&(a=t.avail_out),0!==a&&(t.output.set(e.pending_buf.subarray(e.pending_out,e.pending_out+a),t.next_out),t.next_out+=a,e.pending_out+=a,t.total_out+=a,t.avail_out-=a,e.pending-=a,0===e.pending&&(e.pending_out=0))},xt=function(t,e){G(t,t.block_start>=0?t.block_start:-1,t.strstart-t.block_start,e),t.block_start=t.strstart,kt(t.strm)},At=function(t,e){t.pending_buf[t.pending++]=e},Et=function(t,e){t.pending_buf[t.pending++]=e>>>8&255,t.pending_buf[t.pending++]=255&e},Zt=function(t,e,a,r){var n=t.avail_in;return n>r&&(n=r),0===n?0:(t.avail_in-=n,e.set(t.input.subarray(t.next_in,t.next_in+n),a),1===t.state.wrap?t.adler=B(t.adler,e,n,a):2===t.state.wrap&&(t.adler=M(t.adler,e,n,a)),t.next_in+=n,t.total_in+=n,n)},St=function(t,e){var a,r,n=t.max_chain_length,i=t.strstart,s=t.prev_length,_=t.nice_match,h=t.strstart>t.w_size-ct?t.strstart-(t.w_size-ct):0,o=t.window,l=t.w_mask,d=t.prev,u=t.strstart+ft,f=o[i+s-1],c=o[i+s];t.prev_length>=t.good_match&&(n>>=2),_>t.lookahead&&(_=t.lookahead);do{if(o[(a=e)+s]===c&&o[a+s-1]===f&&o[a]===o[i]&&o[++a]===o[i+1]){i+=2,a++;do{}while(o[++i]===o[++a]&&o[++i]===o[++a]&&o[++i]===o[++a]&&o[++i]===o[++a]&&o[++i]===o[++a]&&o[++i]===o[++a]&&o[++i]===o[++a]&&o[++i]===o[++a]&&i<u);if(r=ft-(u-i),i=u-ft,r>s){if(t.match_start=e,s=r,r>=_)break;f=o[i+s-1],c=o[i+s]}}}while((e=d[e&l])>h&&0!=--n);return s<=t.lookahead?s:t.lookahead},Ut=function(t){var e,a,r,n=t.w_size;do{if(a=t.window_size-t.lookahead-t.strstart,t.strstart>=n+(n-ct)&&(t.window.set(t.window.subarray(n,n+n-a),0),t.match_start-=n,t.strstart-=n,t.block_start-=n,t.insert>t.strstart&&(t.insert=t.strstart),yt(t),a+=n),0===t.strm.avail_in)break;if(e=Zt(t.strm,t.window,t.strstart+t.lookahead,a),t.lookahead+=e,t.lookahead+t.insert>=3)for(r=t.strstart-t.insert,t.ins_h=t.window[r],t.ins_h=zt(t,t.ins_h,t.window[r+1]);t.insert&&(t.ins_h=zt(t,t.ins_h,t.window[r+3-1]),t.prev[r&t.w_mask]=t.head[t.ins_h],t.head[t.ins_h]=r,r++,t.insert--,!(t.lookahead+t.insert<3)););}while(t.lookahead<ct&&0!==t.strm.avail_in)},Rt=function(t,e){var a,r,n,i=t.pending_buf_size-5>t.w_size?t.w_size:t.pending_buf_size-5,s=0,_=t.strm.avail_in;do{if(a=65535,n=t.bi_valid+42>>3,t.strm.avail_out<n)break;if(n=t.strm.avail_out-n,a>(r=t.strstart-t.block_start)+t.strm.avail_in&&(a=r+t.strm.avail_in),a>n&&(a=n),a<i&&(0===a&&e!==V||e===q||a!==r+t.strm.avail_in))break;s=e===V&&a===r+t.strm.avail_in?1:0,Y(t,0,0,s),t.pending_buf[t.pending-4]=a,t.pending_buf[t.pending-3]=a>>8,t.pending_buf[t.pending-2]=~a,t.pending_buf[t.pending-1]=~a>>8,kt(t.strm),r&&(r>a&&(r=a),t.strm.output.set(t.window.subarray(t.block_start,t.block_start+r),t.strm.next_out),t.strm.next_out+=r,t.strm.avail_out-=r,t.strm.total_out+=r,t.block_start+=r,a-=r),a&&(Zt(t.strm,t.strm.output,t.strm.next_out,a),t.strm.next_out+=a,t.strm.avail_out-=a,t.strm.total_out+=a)}while(0===s);return(_-=t.strm.avail_in)&&(_>=t.w_size?(t.matches=2,t.window.set(t.strm.input.subarray(t.strm.next_in-t.w_size,t.strm.next_in),0),t.strstart=t.w_size,t.insert=t.strstart):(t.window_size-t.strstart<=_&&(t.strstart-=t.w_size,t.window.set(t.window.subarray(t.w_size,t.w_size+t.strstart),0),t.matches<2&&t.matches++,t.insert>t.strstart&&(t.insert=t.strstart)),t.window.set(t.strm.input.subarray(t.strm.next_in-_,t.strm.next_in),t.strstart),t.strstart+=_,t.insert+=_>t.w_size-t.insert?t.w_size-t.insert:_),t.block_start=t.strstart),t.high_water<t.strstart&&(t.high_water=t.strstart),s?4:e!==q&&e!==V&&0===t.strm.avail_in&&t.strstart===t.block_start?2:(n=t.window_size-t.strstart,t.strm.avail_in>n&&t.block_start>=t.w_size&&(t.block_start-=t.w_size,t.strstart-=t.w_size,t.window.set(t.window.subarray(t.w_size,t.w_size+t.strstart),0),t.matches<2&&t.matches++,n+=t.w_size,t.insert>t.strstart&&(t.insert=t.strstart)),n>t.strm.avail_in&&(n=t.strm.avail_in),n&&(Zt(t.strm,t.window,t.strstart,n),t.strstart+=n,t.insert+=n>t.w_size-t.insert?t.w_size-t.insert:n),t.high_water<t.strstart&&(t.high_water=t.strstart),n=t.bi_valid+42>>3,i=(n=t.pending_buf_size-n>65535?65535:t.pending_buf_size-n)>t.w_size?t.w_size:n,((r=t.strstart-t.block_start)>=i||(r||e===V)&&e!==q&&0===t.strm.avail_in&&r<=n)&&(a=r>n?n:r,s=e===V&&0===t.strm.avail_in&&a===r?1:0,Y(t,t.block_start,a,s),t.block_start+=a,kt(t.strm)),s?3:1)},Tt=function(t,e){for(var a,r;;){if(t.lookahead<ct){if(Ut(t),t.lookahead<ct&&e===q)return 1;if(0===t.lookahead)break}if(a=0,t.lookahead>=3&&(t.ins_h=zt(t,t.ins_h,t.window[t.strstart+3-1]),a=t.prev[t.strstart&t.w_mask]=t.head[t.ins_h],t.head[t.ins_h]=t.strstart),0!==a&&t.strstart-a<=t.w_size-ct&&(t.match_length=St(t,a)),t.match_length>=3)if(r=X(t,t.strstart-t.match_start,t.match_length-3),t.lookahead-=t.match_length,t.match_length<=t.max_lazy_match&&t.lookahead>=3){t.match_length--;do{t.strstart++,t.ins_h=zt(t,t.ins_h,t.window[t.strstart+3-1]),a=t.prev[t.strstart&t.w_mask]=t.head[t.ins_h],t.head[t.ins_h]=t.strstart}while(0!=--t.match_length);t.strstart++}else t.strstart+=t.match_length,t.match_length=0,t.ins_h=t.window[t.strstart],t.ins_h=zt(t,t.ins_h,t.window[t.strstart+1]);else r=X(t,0,t.window[t.strstart]),t.lookahead--,t.strstart++;if(r&&(xt(t,!1),0===t.strm.avail_out))return 1}return t.insert=t.strstart<2?t.strstart:2,e===V?(xt(t,!0),0===t.strm.avail_out?3:4):t.sym_next&&(xt(t,!1),0===t.strm.avail_out)?1:2},Lt=function(t,e){for(var a,r,n;;){if(t.lookahead<ct){if(Ut(t),t.lookahead<ct&&e===q)return 1;if(0===t.lookahead)break}if(a=0,t.lookahead>=3&&(t.ins_h=zt(t,t.ins_h,t.window[t.strstart+3-1]),a=t.prev[t.strstart&t.w_mask]=t.head[t.ins_h],t.head[t.ins_h]=t.strstart),t.prev_length=t.match_length,t.prev_match=t.match_start,t.match_length=2,0!==a&&t.prev_length<t.max_lazy_match&&t.strstart-a<=t.w_size-ct&&(t.match_length=St(t,a),t.match_length<=5&&(t.strategy===st||3===t.match_length&&t.strstart-t.match_start>4096)&&(t.match_length=2)),t.prev_length>=3&&t.match_length<=t.prev_length){n=t.strstart+t.lookahead-3,r=X(t,t.strstart-1-t.prev_match,t.prev_length-3),t.lookahead-=t.prev_length-1,t.prev_length-=2;do{++t.strstart<=n&&(t.ins_h=zt(t,t.ins_h,t.window[t.strstart+3-1]),a=t.prev[t.strstart&t.w_mask]=t.head[t.ins_h],t.head[t.ins_h]=t.strstart)}while(0!=--t.prev_length);if(t.match_available=0,t.match_length=2,t.strstart++,r&&(xt(t,!1),0===t.strm.avail_out))return 1}else if(t.match_available){if((r=X(t,0,t.window[t.strstart-1]))&&xt(t,!1),t.strstart++,t.lookahead--,0===t.strm.avail_out)return 1}else t.match_available=1,t.strstart++,t.lookahead--}return t.match_available&&(r=X(t,0,t.window[t.strstart-1]),t.match_available=0),t.insert=t.strstart<2?t.strstart:2,e===V?(xt(t,!0),0===t.strm.avail_out?3:4):t.sym_next&&(xt(t,!1),0===t.strm.avail_out)?1:2};function Ft(t,e,a,r,n){this.good_length=t,this.max_lazy=e,this.nice_length=a,this.max_chain=r,this.func=n}var Ot=[new Ft(0,0,0,0,Rt),new Ft(4,4,8,4,Tt),new Ft(4,5,16,8,Tt),new Ft(4,6,32,32,Tt),new Ft(4,4,16,16,Lt),new Ft(8,16,32,32,Lt),new Ft(8,16,128,128,Lt),new Ft(8,32,128,256,Lt),new Ft(32,128,258,1024,Lt),new Ft(32,258,258,4096,Lt)];function Dt(){this.strm=null,this.status=0,this.pending_buf=null,this.pending_buf_size=0,this.pending_out=0,this.pending=0,this.wrap=0,this.gzhead=null,this.gzindex=0,this.method=ut,this.last_flush=-1,this.w_size=0,this.w_bits=0,this.w_mask=0,this.window=null,this.window_size=0,this.prev=null,this.head=null,this.ins_h=0,this.hash_size=0,this.hash_bits=0,this.hash_mask=0,this.hash_shift=0,this.block_start=0,this.match_length=0,this.prev_match=0,this.match_available=0,this.strstart=0,this.match_start=0,this.lookahead=0,this.prev_length=0,this.max_chain_length=0,this.max_lazy_match=0,this.level=0,this.strategy=0,this.good_match=0,this.nice_match=0,this.dyn_ltree=new Uint16Array(1146),this.dyn_dtree=new Uint16Array(122),this.bl_tree=new Uint16Array(78),vt(this.dyn_ltree),vt(this.dyn_dtree),vt(this.bl_tree),this.l_desc=null,this.d_desc=null,this.bl_desc=null,this.bl_count=new Uint16Array(16),this.heap=new Uint16Array(573),vt(this.heap),this.heap_len=0,this.heap_max=0,this.depth=new Uint16Array(573),vt(this.depth),this.sym_buf=0,this.lit_bufsize=0,this.sym_next=0,this.sym_end=0,this.opt_len=0,this.static_len=0,this.matches=0,this.insert=0,this.bi_buf=0,this.bi_valid=0}var Nt=function(t){if(!t)return 1;var e=t.state;return!e||e.strm!==t||e.status!==pt&&57!==e.status&&69!==e.status&&73!==e.status&&91!==e.status&&103!==e.status&&e.status!==gt&&e.status!==wt?1:0},It=function(t){if(Nt(t))return mt(t,at);t.total_in=t.total_out=0,t.data_type=dt;var e=t.state;return e.pending=0,e.pending_out=0,e.wrap<0&&(e.wrap=-e.wrap),e.status=2===e.wrap?57:e.wrap?pt:gt,t.adler=2===e.wrap?0:1,e.last_flush=-2,K(e),tt},Ct=function(t){var e,a=It(t);return a===tt&&((e=t.state).window_size=2*e.w_size,vt(e.head),e.max_lazy_match=Ot[e.level].max_lazy,e.good_match=Ot[e.level].good_length,e.nice_match=Ot[e.level].nice_length,e.max_chain_length=Ot[e.level].max_chain,e.strstart=0,e.block_start=0,e.lookahead=0,e.insert=0,e.match_length=e.prev_length=2,e.match_available=0,e.ins_h=0),a},Bt=function(t,e,a,r,n,i){if(!t)return at;var s=1;if(e===it&&(e=6),r<0?(s=0,r=-r):r>15&&(s=2,r-=16),n<1||n>9||a!==ut||r<8||r>15||e<0||e>9||i<0||i>ot||8===r&&1!==s)return mt(t,at);8===r&&(r=9);var _=new Dt;return t.state=_,_.strm=t,_.status=pt,_.wrap=s,_.gzhead=null,_.w_bits=r,_.w_size=1<<_.w_bits,_.w_mask=_.w_size-1,_.hash_bits=n+7,_.hash_size=1<<_.hash_bits,_.hash_mask=_.hash_size-1,_.hash_shift=~~((_.hash_bits+3-1)/3),_.window=new Uint8Array(2*_.w_size),_.head=new Uint16Array(_.hash_size),_.prev=new Uint16Array(_.w_size),_.lit_bufsize=1<<n+6,_.pending_buf_size=4*_.lit_bufsize,_.pending_buf=new Uint8Array(_.pending_buf_size),_.sym_buf=_.lit_bufsize,_.sym_end=3*(_.lit_bufsize-1),_.level=e,_.strategy=i,_.method=a,Ct(t)},Ht={deflateInit:function(t,e){return Bt(t,e,ut,15,8,lt)},deflateInit2:Bt,deflateReset:Ct,deflateResetKeep:It,deflateSetHeader:function(t,e){return Nt(t)||2!==t.state.wrap?at:(t.state.gzhead=e,tt)},deflate:function(t,e){if(Nt(t)||e>$||e<0)return t?mt(t,at):at;var a=t.state;if(!t.output||0!==t.avail_in&&!t.input||a.status===wt&&e!==V)return mt(t,0===t.avail_out?nt:at);var r=a.last_flush;if(a.last_flush=e,0!==a.pending){if(kt(t),0===t.avail_out)return a.last_flush=-1,tt}else if(0===t.avail_in&&bt(e)<=bt(r)&&e!==V)return mt(t,nt);if(a.status===wt&&0!==t.avail_in)return mt(t,nt);if(a.status===pt&&0===a.wrap&&(a.status=gt),a.status===pt){var n=ut+(a.w_bits-8<<4)<<8;if(n|=(a.strategy>=_t||a.level<2?0:a.level<6?1:6===a.level?2:3)<<6,0!==a.strstart&&(n|=32),Et(a,n+=31-n%31),0!==a.strstart&&(Et(a,t.adler>>>16),Et(a,65535&t.adler)),t.adler=1,a.status=gt,kt(t),0!==a.pending)return a.last_flush=-1,tt}if(57===a.status)if(t.adler=0,At(a,31),At(a,139),At(a,8),a.gzhead)At(a,(a.gzhead.text?1:0)+(a.gzhead.hcrc?2:0)+(a.gzhead.extra?4:0)+(a.gzhead.name?8:0)+(a.gzhead.comment?16:0)),At(a,255&a.gzhead.time),At(a,a.gzhead.time>>8&255),At(a,a.gzhead.time>>16&255),At(a,a.gzhead.time>>24&255),At(a,9===a.level?2:a.strategy>=_t||a.level<2?4:0),At(a,255&a.gzhead.os),a.gzhead.extra&&a.gzhead.extra.length&&(At(a,255&a.gzhead.extra.length),At(a,a.gzhead.extra.length>>8&255)),a.gzhead.hcrc&&(t.adler=M(t.adler,a.pending_buf,a.pending,0)),a.gzindex=0,a.status=69;else if(At(a,0),At(a,0),At(a,0),At(a,0),At(a,0),At(a,9===a.level?2:a.strategy>=_t||a.level<2?4:0),At(a,3),a.status=gt,kt(t),0!==a.pending)return a.last_flush=-1,tt;if(69===a.status){if(a.gzhead.extra){for(var i=a.pending,s=(65535&a.gzhead.extra.length)-a.gzindex;a.pending+s>a.pending_buf_size;){var _=a.pending_buf_size-a.pending;if(a.pending_buf.set(a.gzhead.extra.subarray(a.gzindex,a.gzindex+_),a.pending),a.pending=a.pending_buf_size,a.gzhead.hcrc&&a.pending>i&&(t.adler=M(t.adler,a.pending_buf,a.pending-i,i)),a.gzindex+=_,kt(t),0!==a.pending)return a.last_flush=-1,tt;i=0,s-=_}var h=new Uint8Array(a.gzhead.extra);a.pending_buf.set(h.subarray(a.gzindex,a.gzindex+s),a.pending),a.pending+=s,a.gzhead.hcrc&&a.pending>i&&(t.adler=M(t.adler,a.pending_buf,a.pending-i,i)),a.gzindex=0}a.status=73}if(73===a.status){if(a.gzhead.name){var o,l=a.pending;do{if(a.pending===a.pending_buf_size){if(a.gzhead.hcrc&&a.pending>l&&(t.adler=M(t.adler,a.pending_buf,a.pending-l,l)),kt(t),0!==a.pending)return a.last_flush=-1,tt;l=0}o=a.gzindex<a.gzhead.name.length?255&a.gzhead.name.charCodeAt(a.gzindex++):0,At(a,o)}while(0!==o);a.gzhead.hcrc&&a.pending>l&&(t.adler=M(t.adler,a.pending_buf,a.pending-l,l)),a.gzindex=0}a.status=91}if(91===a.status){if(a.gzhead.comment){var d,u=a.pending;do{if(a.pending===a.pending_buf_size){if(a.gzhead.hcrc&&a.pending>u&&(t.adler=M(t.adler,a.pending_buf,a.pending-u,u)),kt(t),0!==a.pending)return a.last_flush=-1,tt;u=0}d=a.gzindex<a.gzhead.comment.length?255&a.gzhead.comment.charCodeAt(a.gzindex++):0,At(a,d)}while(0!==d);a.gzhead.hcrc&&a.pending>u&&(t.adler=M(t.adler,a.pending_buf,a.pending-u,u))}a.status=103}if(103===a.status){if(a.gzhead.hcrc){if(a.pending+2>a.pending_buf_size&&(kt(t),0!==a.pending))return a.last_flush=-1,tt;At(a,255&t.adler),At(a,t.adler>>8&255),t.adler=0}if(a.status=gt,kt(t),0!==a.pending)return a.last_flush=-1,tt}if(0!==t.avail_in||0!==a.lookahead||e!==q&&a.status!==wt){var f=0===a.level?Rt(a,e):a.strategy===_t?function(t,e){for(var a;;){if(0===t.lookahead&&(Ut(t),0===t.lookahead)){if(e===q)return 1;break}if(t.match_length=0,a=X(t,0,t.window[t.strstart]),t.lookahead--,t.strstart++,a&&(xt(t,!1),0===t.strm.avail_out))return 1}return t.insert=0,e===V?(xt(t,!0),0===t.strm.avail_out?3:4):t.sym_next&&(xt(t,!1),0===t.strm.avail_out)?1:2}(a,e):a.strategy===ht?function(t,e){for(var a,r,n,i,s=t.window;;){if(t.lookahead<=ft){if(Ut(t),t.lookahead<=ft&&e===q)return 1;if(0===t.lookahead)break}if(t.match_length=0,t.lookahead>=3&&t.strstart>0&&(r=s[n=t.strstart-1])===s[++n]&&r===s[++n]&&r===s[++n]){i=t.strstart+ft;do{}while(r===s[++n]&&r===s[++n]&&r===s[++n]&&r===s[++n]&&r===s[++n]&&r===s[++n]&&r===s[++n]&&r===s[++n]&&n<i);t.match_length=ft-(i-n),t.match_length>t.lookahead&&(t.match_length=t.lookahead)}if(t.match_length>=3?(a=X(t,1,t.match_length-3),t.lookahead-=t.match_length,t.strstart+=t.match_length,t.match_length=0):(a=X(t,0,t.window[t.strstart]),t.lookahead--,t.strstart++),a&&(xt(t,!1),0===t.strm.avail_out))return 1}return t.insert=0,e===V?(xt(t,!0),0===t.strm.avail_out?3:4):t.sym_next&&(xt(t,!1),0===t.strm.avail_out)?1:2}(a,e):Ot[a.level].func(a,e);if(3!==f&&4!==f||(a.status=wt),1===f||3===f)return 0===t.avail_out&&(a.last_flush=-1),tt;if(2===f&&(e===J?W(a):e!==$&&(Y(a,0,0,!1),e===Q&&(vt(a.head),0===a.lookahead&&(a.strstart=0,a.block_start=0,a.insert=0))),kt(t),0===t.avail_out))return a.last_flush=-1,tt}return e!==V?tt:a.wrap<=0?et:(2===a.wrap?(At(a,255&t.adler),At(a,t.adler>>8&255),At(a,t.adler>>16&255),At(a,t.adler>>24&255),At(a,255&t.total_in),At(a,t.total_in>>8&255),At(a,t.total_in>>16&255),At(a,t.total_in>>24&255)):(Et(a,t.adler>>>16),Et(a,65535&t.adler)),kt(t),a.wrap>0&&(a.wrap=-a.wrap),0!==a.pending?tt:et)},deflateEnd:function(t){if(Nt(t))return at;var e=t.state.status;return t.state=null,e===gt?mt(t,rt):tt},deflateSetDictionary:function(t,e){var a=e.length;if(Nt(t))return at;var r=t.state,n=r.wrap;if(2===n||1===n&&r.status!==pt||r.lookahead)return at;if(1===n&&(t.adler=B(t.adler,e,a,0)),r.wrap=0,a>=r.w_size){0===n&&(vt(r.head),r.strstart=0,r.block_start=0,r.insert=0);var i=new Uint8Array(r.w_size);i.set(e.subarray(a-r.w_size,a),0),e=i,a=r.w_size}var s=t.avail_in,_=t.next_in,h=t.input;for(t.avail_in=a,t.next_in=0,t.input=e,Ut(r);r.lookahead>=3;){var o=r.strstart,l=r.lookahead-2;do{r.ins_h=zt(r,r.ins_h,r.window[o+3-1]),r.prev[o&r.w_mask]=r.head[r.ins_h],r.head[r.ins_h]=o,o++}while(--l);r.strstart=o,r.lookahead=2,Ut(r)}return r.strstart+=r.lookahead,r.block_start=r.strstart,r.insert=r.lookahead,r.lookahead=0,r.match_length=r.prev_length=2,r.match_available=0,t.next_in=_,t.input=h,t.avail_in=s,r.wrap=n,tt},deflateInfo:"pako deflate (from Nodeca project)"};function Mt(t){return Mt="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Mt(t)}var Pt=function(t,e){return Object.prototype.hasOwnProperty.call(t,e)},jt=function(t){for(var e=Array.prototype.slice.call(arguments,1);e.length;){var a=e.shift();if(a){if("object"!==Mt(a))throw new TypeError(a+"must be non-object");for(var r in a)Pt(a,r)&&(t[r]=a[r])}}return t},Kt=function(t){for(var e=0,a=0,r=t.length;a<r;a++)e+=t[a].length;for(var n=new Uint8Array(e),i=0,s=0,_=t.length;i<_;i++){var h=t[i];n.set(h,s),s+=h.length}return n};try{String.fromCharCode.apply(null,new Uint8Array(1))}catch(t){!1}for(var Yt=new Uint8Array(256),Gt=0;Gt<256;Gt++)Yt[Gt]=Gt>=252?6:Gt>=248?5:Gt>=240?4:Gt>=224?3:Gt>=192?2:1;Yt[254]=Yt[254]=1;var Xt=function(t){if("function"==typeof TextEncoder&&TextEncoder.prototype.encode)return(new TextEncoder).encode(t);var e,a,r,n,i,s=t.length,_=0;for(n=0;n<s;n++)55296==(64512&(a=t.charCodeAt(n)))&&n+1<s&&56320==(64512&(r=t.charCodeAt(n+1)))&&(a=65536+(a-55296<<10)+(r-56320),n++),_+=a<128?1:a<2048?2:a<65536?3:4;for(e=new Uint8Array(_),i=0,n=0;i<_;n++)55296==(64512&(a=t.charCodeAt(n)))&&n+1<s&&56320==(64512&(r=t.charCodeAt(n+1)))&&(a=65536+(a-55296<<10)+(r-56320),n++),a<128?e[i++]=a:a<2048?(e[i++]=192|a>>>6,e[i++]=128|63&a):a<65536?(e[i++]=224|a>>>12,e[i++]=128|a>>>6&63,e[i++]=128|63&a):(e[i++]=240|a>>>18,e[i++]=128|a>>>12&63,e[i++]=128|a>>>6&63,e[i++]=128|63&a);return e};var Wt=function(){this.input=null,this.next_in=0,this.avail_in=0,this.total_in=0,this.output=null,this.next_out=0,this.avail_out=0,this.total_out=0,this.msg="",this.state=null,this.data_type=2,this.adler=0},qt=Object.prototype.toString,Jt=j.Z_NO_FLUSH,Qt=j.Z_SYNC_FLUSH,Vt=j.Z_FULL_FLUSH,$t=j.Z_FINISH,te=j.Z_OK,ee=j.Z_STREAM_END,ae=j.Z_DEFAULT_COMPRESSION,re=j.Z_DEFAULT_STRATEGY,ne=j.Z_DEFLATED;function ie(t){this.options=jt({level:ae,method:ne,chunkSize:16384,windowBits:15,memLevel:8,strategy:re},t||{});var e=this.options;e.raw&&e.windowBits>0?e.windowBits=-e.windowBits:e.gzip&&e.windowBits>0&&e.windowBits<16&&(e.windowBits+=16),this.err=0,this.msg="",this.ended=!1,this.chunks=[],this.strm=new Wt,this.strm.avail_out=0;var a=Ht.deflateInit2(this.strm,e.level,e.method,e.windowBits,e.memLevel,e.strategy);if(a!==te)throw new Error(P[a]);if(e.header&&Ht.deflateSetHeader(this.strm,e.header),e.dictionary){var r;if(r="string"==typeof e.dictionary?Xt(e.dictionary):"[object ArrayBuffer]"===qt.call(e.dictionary)?new Uint8Array(e.dictionary):e.dictionary,(a=Ht.deflateSetDictionary(this.strm,r))!==te)throw new Error(P[a]);this._dict_set=!0}}function se(t,e){var a=new ie(e);if(a.push(t,!0),a.err)throw a.msg||P[a.err];return a.result}ie.prototype.push=function(t,e){var a,r,n=this.strm,i=this.options.chunkSize;if(this.ended)return!1;for(r=e===~~e?e:!0===e?$t:Jt,"string"==typeof t?n.input=Xt(t):"[object ArrayBuffer]"===qt.call(t)?n.input=new Uint8Array(t):n.input=t,n.next_in=0,n.avail_in=n.input.length;;)if(0===n.avail_out&&(n.output=new Uint8Array(i),n.next_out=0,n.avail_out=i),(r===Qt||r===Vt)&&n.avail_out<=6)this.onData(n.output.subarray(0,n.next_out)),n.avail_out=0;else{if((a=Ht.deflate(n,r))===ee)return n.next_out>0&&this.onData(n.output.subarray(0,n.next_out)),a=Ht.deflateEnd(this.strm),this.onEnd(a),this.ended=!0,a===te;if(0!==n.avail_out){if(r>0&&n.next_out>0)this.onData(n.output.subarray(0,n.next_out)),n.avail_out=0;else if(0===n.avail_in)break}else this.onData(n.output)}return!0},ie.prototype.onData=function(t){this.chunks.push(t)},ie.prototype.onEnd=function(t){t===te&&(this.result=Kt(this.chunks)),this.chunks=[],this.err=t,this.msg=this.strm.msg};var _e=ie,he=se,oe=function(t,e){return(e=e||{}).raw=!0,se(t,e)},le=function(t,e){return(e=e||{}).gzip=!0,se(t,e)},de=j,ue={Deflate:_e,deflate:he,deflateRaw:oe,gzip:le,constants:de};t.Deflate=_e,t.constants=de,t.default=ue,t.deflate=he,t.deflateRaw=oe,t.gzip=le,Object.defineProperty(t,"__esModule",{value:!0})}));
