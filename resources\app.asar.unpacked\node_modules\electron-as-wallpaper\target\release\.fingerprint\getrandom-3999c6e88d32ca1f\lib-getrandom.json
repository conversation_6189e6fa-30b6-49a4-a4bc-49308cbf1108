{"rustc": 8713626761367032038, "features": "[]", "declared_features": "[\"compiler_builtins\", \"core\", \"custom\", \"js\", \"js-sys\", \"linux_disable_fallback\", \"rdrand\", \"rustc-dep-of-std\", \"std\", \"test-in-browser\", \"wasm-bindgen\"]", "target": 11884987481660704207, "profile": 18277820415669657429, "path": 1428710550563700753, "deps": [[2452538001284770427, "cfg_if", false, 17464101171816252957]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\getrandom-3999c6e88d32ca1f\\dep-lib-getrandom", "checksum": false}}], "rustflags": [], "metadata": 12606519392706294666, "config": 2202906307356721367, "compile_kind": 0}