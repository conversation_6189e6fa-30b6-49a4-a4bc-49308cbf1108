{"version": 3, "file": "h-helper.js", "sources": ["../../../../../../packages/components/table/src/h-helper.ts"], "sourcesContent": ["// @ts-nocheck\nimport { h } from 'vue'\nimport { isUndefined } from '@element-plus/utils'\n\nexport function hColgroup(props) {\n  const isAuto = props.tableLayout === 'auto'\n  let columns = props.columns || []\n  if (isAuto) {\n    if (columns.every(({ width }) => isUndefined(width))) {\n      columns = []\n    }\n  }\n  const getPropsData = (column) => {\n    const propsData = {\n      key: `${props.tableLayout}_${column.id}`,\n      style: {},\n      name: undefined,\n    }\n    if (isAuto) {\n      propsData.style = {\n        width: `${column.width}px`,\n      }\n    } else {\n      propsData.name = column.id\n    }\n    return propsData\n  }\n\n  return h(\n    'colgroup',\n    {},\n    columns.map((column) => h('col', getPropsData(column)))\n  )\n}\n\nhColgroup.props = ['columns', 'tableLayout']\n"], "names": ["isUndefined", "h"], "mappings": ";;;;;;;AAEO,SAAS,SAAS,CAAC,KAAK,EAAE;AACjC,EAAE,MAAM,MAAM,GAAG,KAAK,CAAC,WAAW,KAAK,MAAM,CAAC;AAC9C,EAAE,IAAI,OAAO,GAAG,KAAK,CAAC,OAAO,IAAI,EAAE,CAAC;AACpC,EAAE,IAAI,MAAM,EAAE;AACd,IAAI,IAAI,OAAO,CAAC,KAAK,CAAC,CAAC,EAAE,KAAK,EAAE,KAAKA,iBAAW,CAAC,KAAK,CAAC,CAAC,EAAE;AAC1D,MAAM,OAAO,GAAG,EAAE,CAAC;AACnB,KAAK;AACL,GAAG;AACH,EAAE,MAAM,YAAY,GAAG,CAAC,MAAM,KAAK;AACnC,IAAI,MAAM,SAAS,GAAG;AACtB,MAAM,GAAG,EAAE,CAAC,EAAE,KAAK,CAAC,WAAW,CAAC,CAAC,EAAE,MAAM,CAAC,EAAE,CAAC,CAAC;AAC9C,MAAM,KAAK,EAAE,EAAE;AACf,MAAM,IAAI,EAAE,KAAK,CAAC;AAClB,KAAK,CAAC;AACN,IAAI,IAAI,MAAM,EAAE;AAChB,MAAM,SAAS,CAAC,KAAK,GAAG;AACxB,QAAQ,KAAK,EAAE,CAAC,EAAE,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC;AAClC,OAAO,CAAC;AACR,KAAK,MAAM;AACX,MAAM,SAAS,CAAC,IAAI,GAAG,MAAM,CAAC,EAAE,CAAC;AACjC,KAAK;AACL,IAAI,OAAO,SAAS,CAAC;AACrB,GAAG,CAAC;AACJ,EAAE,OAAOC,KAAC,CAAC,UAAU,EAAE,EAAE,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC,MAAM,KAAKA,KAAC,CAAC,KAAK,EAAE,YAAY,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;AACpF,CAAC;AACD,SAAS,CAAC,KAAK,GAAG,CAAC,SAAS,EAAE,aAAa,CAAC;;;;"}