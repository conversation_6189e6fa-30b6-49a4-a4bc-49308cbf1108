{"version": 3, "file": "use-slider-button.js", "sources": ["../../../../../../../packages/components/slider/src/composables/use-slider-button.ts"], "sourcesContent": ["import { computed, inject, nextTick, ref, watch } from 'vue'\nimport { debounce } from 'lodash-unified'\nimport { useEventListener } from '@vueuse/core'\nimport { EVENT_CODE, UPDATE_MODEL_EVENT } from '@element-plus/constants'\nimport { sliderContextKey } from '../constants'\n\nimport type { CSSProperties, ComputedRef, Ref, SetupContext } from 'vue'\nimport type { SliderProps } from '../slider'\nimport type {\n  SliderButtonEmits,\n  SliderButtonInitData,\n  SliderButtonProps,\n} from '../button'\nimport type { TooltipInstance } from '@element-plus/components/tooltip'\n\nconst useTooltip = (\n  props: SliderButtonProps,\n  formatTooltip: Ref<SliderProps['formatTooltip']>,\n  showTooltip: Ref<SliderProps['showTooltip']>\n) => {\n  const tooltip = ref<TooltipInstance>()\n\n  const tooltipVisible = ref(false)\n\n  const enableFormat = computed(() => {\n    return formatTooltip.value instanceof Function\n  })\n\n  const formatValue = computed(() => {\n    return (\n      (enableFormat.value && formatTooltip.value!(props.modelValue)) ||\n      props.modelValue\n    )\n  })\n\n  const displayTooltip = debounce(() => {\n    showTooltip.value && (tooltipVisible.value = true)\n  }, 50)\n\n  const hideTooltip = debounce(() => {\n    showTooltip.value && (tooltipVisible.value = false)\n  }, 50)\n\n  return {\n    tooltip,\n    tooltipVisible,\n    formatValue,\n    displayTooltip,\n    hideTooltip,\n  }\n}\n\nexport const useSliderButton = (\n  props: SliderButtonProps,\n  initData: SliderButtonInitData,\n  emit: SetupContext<SliderButtonEmits>['emit']\n) => {\n  const {\n    disabled,\n    min,\n    max,\n    step,\n    showTooltip,\n    persistent,\n    precision,\n    sliderSize,\n    formatTooltip,\n    emitChange,\n    resetSize,\n    updateDragging,\n  } = inject(sliderContextKey)!\n\n  const { tooltip, tooltipVisible, formatValue, displayTooltip, hideTooltip } =\n    useTooltip(props, formatTooltip!, showTooltip)\n\n  const button = ref<HTMLDivElement>()\n\n  const currentPosition = computed(() => {\n    return `${\n      ((props.modelValue - min.value) / (max.value - min.value)) * 100\n    }%`\n  })\n\n  const wrapperStyle: ComputedRef<CSSProperties> = computed(() => {\n    return props.vertical\n      ? { bottom: currentPosition.value }\n      : { left: currentPosition.value }\n  })\n\n  const handleMouseEnter = () => {\n    initData.hovering = true\n    displayTooltip()\n  }\n\n  const handleMouseLeave = () => {\n    initData.hovering = false\n    if (!initData.dragging) {\n      hideTooltip()\n    }\n  }\n\n  const onButtonDown = (event: MouseEvent | TouchEvent) => {\n    if (disabled.value) return\n    event.preventDefault()\n    onDragStart(event)\n    window.addEventListener('mousemove', onDragging)\n    window.addEventListener('touchmove', onDragging)\n    window.addEventListener('mouseup', onDragEnd)\n    window.addEventListener('touchend', onDragEnd)\n    window.addEventListener('contextmenu', onDragEnd)\n    button.value!.focus()\n  }\n\n  const incrementPosition = (amount: number) => {\n    if (disabled.value) return\n    initData.newPosition =\n      Number.parseFloat(currentPosition.value) +\n      (amount / (max.value - min.value)) * 100\n    setPosition(initData.newPosition)\n    emitChange()\n  }\n\n  const onLeftKeyDown = () => {\n    incrementPosition(-step.value)\n  }\n\n  const onRightKeyDown = () => {\n    incrementPosition(step.value)\n  }\n\n  const onPageDownKeyDown = () => {\n    incrementPosition(-step.value * 4)\n  }\n\n  const onPageUpKeyDown = () => {\n    incrementPosition(step.value * 4)\n  }\n\n  const onHomeKeyDown = () => {\n    if (disabled.value) return\n    setPosition(0)\n    emitChange()\n  }\n\n  const onEndKeyDown = () => {\n    if (disabled.value) return\n    setPosition(100)\n    emitChange()\n  }\n\n  const onKeyDown = (event: KeyboardEvent) => {\n    let isPreventDefault = true\n\n    switch (event.code) {\n      case EVENT_CODE.left:\n      case EVENT_CODE.down:\n        onLeftKeyDown()\n        break\n      case EVENT_CODE.right:\n      case EVENT_CODE.up:\n        onRightKeyDown()\n        break\n      case EVENT_CODE.home:\n        onHomeKeyDown()\n        break\n      case EVENT_CODE.end:\n        onEndKeyDown()\n        break\n      case EVENT_CODE.pageDown:\n        onPageDownKeyDown()\n        break\n      case EVENT_CODE.pageUp:\n        onPageUpKeyDown()\n        break\n      default:\n        isPreventDefault = false\n        break\n    }\n\n    isPreventDefault && event.preventDefault()\n  }\n\n  const getClientXY = (event: MouseEvent | TouchEvent) => {\n    let clientX: number\n    let clientY: number\n    if (event.type.startsWith('touch')) {\n      clientY = (event as TouchEvent).touches[0].clientY\n      clientX = (event as TouchEvent).touches[0].clientX\n    } else {\n      clientY = (event as MouseEvent).clientY\n      clientX = (event as MouseEvent).clientX\n    }\n    return {\n      clientX,\n      clientY,\n    }\n  }\n\n  const onDragStart = (event: MouseEvent | TouchEvent) => {\n    initData.dragging = true\n    initData.isClick = true\n    const { clientX, clientY } = getClientXY(event)\n    if (props.vertical) {\n      initData.startY = clientY\n    } else {\n      initData.startX = clientX\n    }\n    initData.startPosition = Number.parseFloat(currentPosition.value)\n    initData.newPosition = initData.startPosition\n  }\n\n  const onDragging = (event: MouseEvent | TouchEvent) => {\n    if (initData.dragging) {\n      initData.isClick = false\n      displayTooltip()\n      resetSize()\n      let diff: number\n      const { clientX, clientY } = getClientXY(event)\n      if (props.vertical) {\n        initData.currentY = clientY\n        diff = ((initData.startY - initData.currentY) / sliderSize.value) * 100\n      } else {\n        initData.currentX = clientX\n        diff = ((initData.currentX - initData.startX) / sliderSize.value) * 100\n      }\n      initData.newPosition = initData.startPosition + diff\n      setPosition(initData.newPosition)\n    }\n  }\n\n  const onDragEnd = () => {\n    if (initData.dragging) {\n      /*\n       * 防止在 mouseup 后立即触发 click，导致滑块有几率产生一小段位移\n       * 不使用 preventDefault 是因为 mouseup 和 click 没有注册在同一个 DOM 上\n       */\n      setTimeout(() => {\n        initData.dragging = false\n        if (!initData.hovering) {\n          hideTooltip()\n        }\n        if (!initData.isClick) {\n          setPosition(initData.newPosition)\n        }\n        emitChange()\n      }, 0)\n      window.removeEventListener('mousemove', onDragging)\n      window.removeEventListener('touchmove', onDragging)\n      window.removeEventListener('mouseup', onDragEnd)\n      window.removeEventListener('touchend', onDragEnd)\n      window.removeEventListener('contextmenu', onDragEnd)\n    }\n  }\n\n  const setPosition = async (newPosition: number) => {\n    if (newPosition === null || Number.isNaN(+newPosition)) return\n    if (newPosition < 0) {\n      newPosition = 0\n    } else if (newPosition > 100) {\n      newPosition = 100\n    }\n    const lengthPerStep = 100 / ((max.value - min.value) / step.value)\n    const steps = Math.round(newPosition / lengthPerStep)\n    let value =\n      steps * lengthPerStep * (max.value - min.value) * 0.01 + min.value\n    value = Number.parseFloat(value.toFixed(precision.value))\n\n    if (value !== props.modelValue) {\n      emit(UPDATE_MODEL_EVENT, value)\n    }\n\n    if (!initData.dragging && props.modelValue !== initData.oldValue) {\n      initData.oldValue = props.modelValue\n    }\n\n    await nextTick()\n    initData.dragging && displayTooltip()\n    tooltip.value!.updatePopper()\n  }\n\n  watch(\n    () => initData.dragging,\n    (val) => {\n      updateDragging(val)\n    }\n  )\n\n  useEventListener(button, 'touchstart', onButtonDown, { passive: false })\n\n  return {\n    disabled,\n    button,\n    tooltip,\n    tooltipVisible,\n    showTooltip,\n    persistent,\n    wrapperStyle,\n    formatValue,\n    handleMouseEnter,\n    handleMouseLeave,\n    onButtonDown,\n    onKeyDown,\n    setPosition,\n  }\n}\n"], "names": ["ref", "computed", "debounce", "inject", "slider<PERSON><PERSON>xt<PERSON><PERSON>", "EVENT_CODE", "UPDATE_MODEL_EVENT", "nextTick", "watch", "useEventListener"], "mappings": ";;;;;;;;;;;AAKA,MAAM,UAAU,GAAG,CAAC,KAAK,EAAE,aAAa,EAAE,WAAW,KAAK;AAC1D,EAAE,MAAM,OAAO,GAAGA,OAAG,EAAE,CAAC;AACxB,EAAE,MAAM,cAAc,GAAGA,OAAG,CAAC,KAAK,CAAC,CAAC;AACpC,EAAE,MAAM,YAAY,GAAGC,YAAQ,CAAC,MAAM;AACtC,IAAI,OAAO,aAAa,CAAC,KAAK,YAAY,QAAQ,CAAC;AACnD,GAAG,CAAC,CAAC;AACL,EAAE,MAAM,WAAW,GAAGA,YAAQ,CAAC,MAAM;AACrC,IAAI,OAAO,YAAY,CAAC,KAAK,IAAI,aAAa,CAAC,KAAK,CAAC,KAAK,CAAC,UAAU,CAAC,IAAI,KAAK,CAAC,UAAU,CAAC;AAC3F,GAAG,CAAC,CAAC;AACL,EAAE,MAAM,cAAc,GAAGC,sBAAQ,CAAC,MAAM;AACxC,IAAI,WAAW,CAAC,KAAK,KAAK,cAAc,CAAC,KAAK,GAAG,IAAI,CAAC,CAAC;AACvD,GAAG,EAAE,EAAE,CAAC,CAAC;AACT,EAAE,MAAM,WAAW,GAAGA,sBAAQ,CAAC,MAAM;AACrC,IAAI,WAAW,CAAC,KAAK,KAAK,cAAc,CAAC,KAAK,GAAG,KAAK,CAAC,CAAC;AACxD,GAAG,EAAE,EAAE,CAAC,CAAC;AACT,EAAE,OAAO;AACT,IAAI,OAAO;AACX,IAAI,cAAc;AAClB,IAAI,WAAW;AACf,IAAI,cAAc;AAClB,IAAI,WAAW;AACf,GAAG,CAAC;AACJ,CAAC,CAAC;AACU,MAAC,eAAe,GAAG,CAAC,KAAK,EAAE,QAAQ,EAAE,IAAI,KAAK;AAC1D,EAAE,MAAM;AACR,IAAI,QAAQ;AACZ,IAAI,GAAG;AACP,IAAI,GAAG;AACP,IAAI,IAAI;AACR,IAAI,WAAW;AACf,IAAI,UAAU;AACd,IAAI,SAAS;AACb,IAAI,UAAU;AACd,IAAI,aAAa;AACjB,IAAI,UAAU;AACd,IAAI,SAAS;AACb,IAAI,cAAc;AAClB,GAAG,GAAGC,UAAM,CAACC,0BAAgB,CAAC,CAAC;AAC/B,EAAE,MAAM,EAAE,OAAO,EAAE,cAAc,EAAE,WAAW,EAAE,cAAc,EAAE,WAAW,EAAE,GAAG,UAAU,CAAC,KAAK,EAAE,aAAa,EAAE,WAAW,CAAC,CAAC;AAC9H,EAAE,MAAM,MAAM,GAAGJ,OAAG,EAAE,CAAC;AACvB,EAAE,MAAM,eAAe,GAAGC,YAAQ,CAAC,MAAM;AACzC,IAAI,OAAO,CAAC,EAAE,CAAC,KAAK,CAAC,UAAU,GAAG,GAAG,CAAC,KAAK,KAAK,GAAG,CAAC,KAAK,GAAG,GAAG,CAAC,KAAK,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC;AAChF,GAAG,CAAC,CAAC;AACL,EAAE,MAAM,YAAY,GAAGA,YAAQ,CAAC,MAAM;AACtC,IAAI,OAAO,KAAK,CAAC,QAAQ,GAAG,EAAE,MAAM,EAAE,eAAe,CAAC,KAAK,EAAE,GAAG,EAAE,IAAI,EAAE,eAAe,CAAC,KAAK,EAAE,CAAC;AAChG,GAAG,CAAC,CAAC;AACL,EAAE,MAAM,gBAAgB,GAAG,MAAM;AACjC,IAAI,QAAQ,CAAC,QAAQ,GAAG,IAAI,CAAC;AAC7B,IAAI,cAAc,EAAE,CAAC;AACrB,GAAG,CAAC;AACJ,EAAE,MAAM,gBAAgB,GAAG,MAAM;AACjC,IAAI,QAAQ,CAAC,QAAQ,GAAG,KAAK,CAAC;AAC9B,IAAI,IAAI,CAAC,QAAQ,CAAC,QAAQ,EAAE;AAC5B,MAAM,WAAW,EAAE,CAAC;AACpB,KAAK;AACL,GAAG,CAAC;AACJ,EAAE,MAAM,YAAY,GAAG,CAAC,KAAK,KAAK;AAClC,IAAI,IAAI,QAAQ,CAAC,KAAK;AACtB,MAAM,OAAO;AACb,IAAI,KAAK,CAAC,cAAc,EAAE,CAAC;AAC3B,IAAI,WAAW,CAAC,KAAK,CAAC,CAAC;AACvB,IAAI,MAAM,CAAC,gBAAgB,CAAC,WAAW,EAAE,UAAU,CAAC,CAAC;AACrD,IAAI,MAAM,CAAC,gBAAgB,CAAC,WAAW,EAAE,UAAU,CAAC,CAAC;AACrD,IAAI,MAAM,CAAC,gBAAgB,CAAC,SAAS,EAAE,SAAS,CAAC,CAAC;AAClD,IAAI,MAAM,CAAC,gBAAgB,CAAC,UAAU,EAAE,SAAS,CAAC,CAAC;AACnD,IAAI,MAAM,CAAC,gBAAgB,CAAC,aAAa,EAAE,SAAS,CAAC,CAAC;AACtD,IAAI,MAAM,CAAC,KAAK,CAAC,KAAK,EAAE,CAAC;AACzB,GAAG,CAAC;AACJ,EAAE,MAAM,iBAAiB,GAAG,CAAC,MAAM,KAAK;AACxC,IAAI,IAAI,QAAQ,CAAC,KAAK;AACtB,MAAM,OAAO;AACb,IAAI,QAAQ,CAAC,WAAW,GAAG,MAAM,CAAC,UAAU,CAAC,eAAe,CAAC,KAAK,CAAC,GAAG,MAAM,IAAI,GAAG,CAAC,KAAK,GAAG,GAAG,CAAC,KAAK,CAAC,GAAG,GAAG,CAAC;AAC7G,IAAI,WAAW,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAC;AACtC,IAAI,UAAU,EAAE,CAAC;AACjB,GAAG,CAAC;AACJ,EAAE,MAAM,aAAa,GAAG,MAAM;AAC9B,IAAI,iBAAiB,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;AACnC,GAAG,CAAC;AACJ,EAAE,MAAM,cAAc,GAAG,MAAM;AAC/B,IAAI,iBAAiB,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;AAClC,GAAG,CAAC;AACJ,EAAE,MAAM,iBAAiB,GAAG,MAAM;AAClC,IAAI,iBAAiB,CAAC,CAAC,IAAI,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC;AACvC,GAAG,CAAC;AACJ,EAAE,MAAM,eAAe,GAAG,MAAM;AAChC,IAAI,iBAAiB,CAAC,IAAI,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC;AACtC,GAAG,CAAC;AACJ,EAAE,MAAM,aAAa,GAAG,MAAM;AAC9B,IAAI,IAAI,QAAQ,CAAC,KAAK;AACtB,MAAM,OAAO;AACb,IAAI,WAAW,CAAC,CAAC,CAAC,CAAC;AACnB,IAAI,UAAU,EAAE,CAAC;AACjB,GAAG,CAAC;AACJ,EAAE,MAAM,YAAY,GAAG,MAAM;AAC7B,IAAI,IAAI,QAAQ,CAAC,KAAK;AACtB,MAAM,OAAO;AACb,IAAI,WAAW,CAAC,GAAG,CAAC,CAAC;AACrB,IAAI,UAAU,EAAE,CAAC;AACjB,GAAG,CAAC;AACJ,EAAE,MAAM,SAAS,GAAG,CAAC,KAAK,KAAK;AAC/B,IAAI,IAAI,gBAAgB,GAAG,IAAI,CAAC;AAChC,IAAI,QAAQ,KAAK,CAAC,IAAI;AACtB,MAAM,KAAKI,eAAU,CAAC,IAAI,CAAC;AAC3B,MAAM,KAAKA,eAAU,CAAC,IAAI;AAC1B,QAAQ,aAAa,EAAE,CAAC;AACxB,QAAQ,MAAM;AACd,MAAM,KAAKA,eAAU,CAAC,KAAK,CAAC;AAC5B,MAAM,KAAKA,eAAU,CAAC,EAAE;AACxB,QAAQ,cAAc,EAAE,CAAC;AACzB,QAAQ,MAAM;AACd,MAAM,KAAKA,eAAU,CAAC,IAAI;AAC1B,QAAQ,aAAa,EAAE,CAAC;AACxB,QAAQ,MAAM;AACd,MAAM,KAAKA,eAAU,CAAC,GAAG;AACzB,QAAQ,YAAY,EAAE,CAAC;AACvB,QAAQ,MAAM;AACd,MAAM,KAAKA,eAAU,CAAC,QAAQ;AAC9B,QAAQ,iBAAiB,EAAE,CAAC;AAC5B,QAAQ,MAAM;AACd,MAAM,KAAKA,eAAU,CAAC,MAAM;AAC5B,QAAQ,eAAe,EAAE,CAAC;AAC1B,QAAQ,MAAM;AACd,MAAM;AACN,QAAQ,gBAAgB,GAAG,KAAK,CAAC;AACjC,QAAQ,MAAM;AACd,KAAK;AACL,IAAI,gBAAgB,IAAI,KAAK,CAAC,cAAc,EAAE,CAAC;AAC/C,GAAG,CAAC;AACJ,EAAE,MAAM,WAAW,GAAG,CAAC,KAAK,KAAK;AACjC,IAAI,IAAI,OAAO,CAAC;AAChB,IAAI,IAAI,OAAO,CAAC;AAChB,IAAI,IAAI,KAAK,CAAC,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,EAAE;AACxC,MAAM,OAAO,GAAG,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC;AACzC,MAAM,OAAO,GAAG,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC;AACzC,KAAK,MAAM;AACX,MAAM,OAAO,GAAG,KAAK,CAAC,OAAO,CAAC;AAC9B,MAAM,OAAO,GAAG,KAAK,CAAC,OAAO,CAAC;AAC9B,KAAK;AACL,IAAI,OAAO;AACX,MAAM,OAAO;AACb,MAAM,OAAO;AACb,KAAK,CAAC;AACN,GAAG,CAAC;AACJ,EAAE,MAAM,WAAW,GAAG,CAAC,KAAK,KAAK;AACjC,IAAI,QAAQ,CAAC,QAAQ,GAAG,IAAI,CAAC;AAC7B,IAAI,QAAQ,CAAC,OAAO,GAAG,IAAI,CAAC;AAC5B,IAAI,MAAM,EAAE,OAAO,EAAE,OAAO,EAAE,GAAG,WAAW,CAAC,KAAK,CAAC,CAAC;AACpD,IAAI,IAAI,KAAK,CAAC,QAAQ,EAAE;AACxB,MAAM,QAAQ,CAAC,MAAM,GAAG,OAAO,CAAC;AAChC,KAAK,MAAM;AACX,MAAM,QAAQ,CAAC,MAAM,GAAG,OAAO,CAAC;AAChC,KAAK;AACL,IAAI,QAAQ,CAAC,aAAa,GAAG,MAAM,CAAC,UAAU,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC;AACtE,IAAI,QAAQ,CAAC,WAAW,GAAG,QAAQ,CAAC,aAAa,CAAC;AAClD,GAAG,CAAC;AACJ,EAAE,MAAM,UAAU,GAAG,CAAC,KAAK,KAAK;AAChC,IAAI,IAAI,QAAQ,CAAC,QAAQ,EAAE;AAC3B,MAAM,QAAQ,CAAC,OAAO,GAAG,KAAK,CAAC;AAC/B,MAAM,cAAc,EAAE,CAAC;AACvB,MAAM,SAAS,EAAE,CAAC;AAClB,MAAM,IAAI,IAAI,CAAC;AACf,MAAM,MAAM,EAAE,OAAO,EAAE,OAAO,EAAE,GAAG,WAAW,CAAC,KAAK,CAAC,CAAC;AACtD,MAAM,IAAI,KAAK,CAAC,QAAQ,EAAE;AAC1B,QAAQ,QAAQ,CAAC,QAAQ,GAAG,OAAO,CAAC;AACpC,QAAQ,IAAI,GAAG,CAAC,QAAQ,CAAC,MAAM,GAAG,QAAQ,CAAC,QAAQ,IAAI,UAAU,CAAC,KAAK,GAAG,GAAG,CAAC;AAC9E,OAAO,MAAM;AACb,QAAQ,QAAQ,CAAC,QAAQ,GAAG,OAAO,CAAC;AACpC,QAAQ,IAAI,GAAG,CAAC,QAAQ,CAAC,QAAQ,GAAG,QAAQ,CAAC,MAAM,IAAI,UAAU,CAAC,KAAK,GAAG,GAAG,CAAC;AAC9E,OAAO;AACP,MAAM,QAAQ,CAAC,WAAW,GAAG,QAAQ,CAAC,aAAa,GAAG,IAAI,CAAC;AAC3D,MAAM,WAAW,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAC;AACxC,KAAK;AACL,GAAG,CAAC;AACJ,EAAE,MAAM,SAAS,GAAG,MAAM;AAC1B,IAAI,IAAI,QAAQ,CAAC,QAAQ,EAAE;AAC3B,MAAM,UAAU,CAAC,MAAM;AACvB,QAAQ,QAAQ,CAAC,QAAQ,GAAG,KAAK,CAAC;AAClC,QAAQ,IAAI,CAAC,QAAQ,CAAC,QAAQ,EAAE;AAChC,UAAU,WAAW,EAAE,CAAC;AACxB,SAAS;AACT,QAAQ,IAAI,CAAC,QAAQ,CAAC,OAAO,EAAE;AAC/B,UAAU,WAAW,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAC;AAC5C,SAAS;AACT,QAAQ,UAAU,EAAE,CAAC;AACrB,OAAO,EAAE,CAAC,CAAC,CAAC;AACZ,MAAM,MAAM,CAAC,mBAAmB,CAAC,WAAW,EAAE,UAAU,CAAC,CAAC;AAC1D,MAAM,MAAM,CAAC,mBAAmB,CAAC,WAAW,EAAE,UAAU,CAAC,CAAC;AAC1D,MAAM,MAAM,CAAC,mBAAmB,CAAC,SAAS,EAAE,SAAS,CAAC,CAAC;AACvD,MAAM,MAAM,CAAC,mBAAmB,CAAC,UAAU,EAAE,SAAS,CAAC,CAAC;AACxD,MAAM,MAAM,CAAC,mBAAmB,CAAC,aAAa,EAAE,SAAS,CAAC,CAAC;AAC3D,KAAK;AACL,GAAG,CAAC;AACJ,EAAE,MAAM,WAAW,GAAG,OAAO,WAAW,KAAK;AAC7C,IAAI,IAAI,WAAW,KAAK,IAAI,IAAI,MAAM,CAAC,KAAK,CAAC,CAAC,WAAW,CAAC;AAC1D,MAAM,OAAO;AACb,IAAI,IAAI,WAAW,GAAG,CAAC,EAAE;AACzB,MAAM,WAAW,GAAG,CAAC,CAAC;AACtB,KAAK,MAAM,IAAI,WAAW,GAAG,GAAG,EAAE;AAClC,MAAM,WAAW,GAAG,GAAG,CAAC;AACxB,KAAK;AACL,IAAI,MAAM,aAAa,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,GAAG,GAAG,CAAC,KAAK,IAAI,IAAI,CAAC,KAAK,CAAC,CAAC;AACvE,IAAI,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,WAAW,GAAG,aAAa,CAAC,CAAC;AAC1D,IAAI,IAAI,KAAK,GAAG,KAAK,GAAG,aAAa,IAAI,GAAG,CAAC,KAAK,GAAG,GAAG,CAAC,KAAK,CAAC,GAAG,IAAI,GAAG,GAAG,CAAC,KAAK,CAAC;AACnF,IAAI,KAAK,GAAG,MAAM,CAAC,UAAU,CAAC,KAAK,CAAC,OAAO,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,CAAC;AAC9D,IAAI,IAAI,KAAK,KAAK,KAAK,CAAC,UAAU,EAAE;AACpC,MAAM,IAAI,CAACC,wBAAkB,EAAE,KAAK,CAAC,CAAC;AACtC,KAAK;AACL,IAAI,IAAI,CAAC,QAAQ,CAAC,QAAQ,IAAI,KAAK,CAAC,UAAU,KAAK,QAAQ,CAAC,QAAQ,EAAE;AACtE,MAAM,QAAQ,CAAC,QAAQ,GAAG,KAAK,CAAC,UAAU,CAAC;AAC3C,KAAK;AACL,IAAI,MAAMC,YAAQ,EAAE,CAAC;AACrB,IAAI,QAAQ,CAAC,QAAQ,IAAI,cAAc,EAAE,CAAC;AAC1C,IAAI,OAAO,CAAC,KAAK,CAAC,YAAY,EAAE,CAAC;AACjC,GAAG,CAAC;AACJ,EAAEC,SAAK,CAAC,MAAM,QAAQ,CAAC,QAAQ,EAAE,CAAC,GAAG,KAAK;AAC1C,IAAI,cAAc,CAAC,GAAG,CAAC,CAAC;AACxB,GAAG,CAAC,CAAC;AACL,EAAEC,qBAAgB,CAAC,MAAM,EAAE,YAAY,EAAE,YAAY,EAAE,EAAE,OAAO,EAAE,KAAK,EAAE,CAAC,CAAC;AAC3E,EAAE,OAAO;AACT,IAAI,QAAQ;AACZ,IAAI,MAAM;AACV,IAAI,OAAO;AACX,IAAI,cAAc;AAClB,IAAI,WAAW;AACf,IAAI,UAAU;AACd,IAAI,YAAY;AAChB,IAAI,WAAW;AACf,IAAI,gBAAgB;AACpB,IAAI,gBAAgB;AACpB,IAAI,YAAY;AAChB,IAAI,SAAS;AACb,IAAI,WAAW;AACf,GAAG,CAAC;AACJ;;;;"}