/*!
  * vue-i18n v11.1.2
  * (c) 2025 ka<PERSON><PERSON> ka<PERSON>
  * Released under the MIT License.
  */
import{createVNode,Text,computed,watch,getCurrentInstance,ref,shallowRef,Fragment,defineComponent,h,effectScope,inject,onMounted,onUnmounted,isRef}from"vue";const inBrowser="undefined"!=typeof window,makeSymbol=(e,t=!1)=>t?Symbol.for(e):Symbol(e),generateFormatCacheKey=(e,t,n)=>friendlyJSONstringify({l:e,k:t,s:n}),friendlyJSONstringify=e=>JSON.stringify(e).replace(/\u2028/g,"\\u2028").replace(/\u2029/g,"\\u2029").replace(/\u0027/g,"\\u0027"),isNumber=e=>"number"==typeof e&&isFinite(e),isDate=e=>"[object Date]"===toTypeString(e),isRegExp=e=>"[object RegExp]"===toTypeString(e),isEmptyObject=e=>isPlainObject(e)&&0===Object.keys(e).length,assign=Object.assign,_create=Object.create,create=(e=null)=>_create(e);function escapeHtml(e){return e.replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/"/g,"&quot;").replace(/'/g,"&apos;")}const hasOwnProperty=Object.prototype.hasOwnProperty;function hasOwn(e,t){return hasOwnProperty.call(e,t)}const isArray=Array.isArray,isFunction=e=>"function"==typeof e,isString=e=>"string"==typeof e,isBoolean=e=>"boolean"==typeof e,isObject=e=>null!==e&&"object"==typeof e,isPromise=e=>isObject(e)&&isFunction(e.then)&&isFunction(e.catch),objectToString=Object.prototype.toString,toTypeString=e=>objectToString.call(e),isPlainObject=e=>"[object Object]"===toTypeString(e),toDisplayString=e=>null==e?"":isArray(e)||isPlainObject(e)&&e.toString===objectToString?JSON.stringify(e,null,2):String(e);function join(e,t=""){return e.reduce(((e,n,r)=>0===r?e+n:e+t+n),"")}function warn(e,t){"undefined"!=typeof console&&(console.warn("[intlify] "+e),t&&console.warn(t.stack))}const isNotObjectOrIsArray=e=>!isObject(e)||isArray(e);function deepCopy(e,t){if(isNotObjectOrIsArray(e)||isNotObjectOrIsArray(t))throw new Error("Invalid value");const n=[{src:e,des:t}];for(;n.length;){const{src:e,des:t}=n.pop();Object.keys(e).forEach((r=>{"__proto__"!==r&&(isObject(e[r])&&!isObject(t[r])&&(t[r]=Array.isArray(e[r])?[]:create()),isNotObjectOrIsArray(t[r])||isNotObjectOrIsArray(e[r])?t[r]=e[r]:n.push({src:e[r],des:t[r]}))}))}}function createPosition(e,t,n){return{line:e,column:t,offset:n}}function createLocation(e,t,n){return{start:e,end:t}}const CompileErrorCodes={EXPECTED_TOKEN:1,INVALID_TOKEN_IN_PLACEHOLDER:2,UNTERMINATED_SINGLE_QUOTE_IN_PLACEHOLDER:3,UNKNOWN_ESCAPE_SEQUENCE:4,INVALID_UNICODE_ESCAPE_SEQUENCE:5,UNBALANCED_CLOSING_BRACE:6,UNTERMINATED_CLOSING_BRACE:7,EMPTY_PLACEHOLDER:8,NOT_ALLOW_NEST_PLACEHOLDER:9,INVALID_LINKED_FORMAT:10,MUST_HAVE_MESSAGES_IN_PLURAL:11,UNEXPECTED_EMPTY_LINKED_MODIFIER:12,UNEXPECTED_EMPTY_LINKED_KEY:13,UNEXPECTED_LEXICAL_ANALYSIS:14,UNHANDLED_CODEGEN_NODE_TYPE:15,UNHANDLED_MINIFIER_NODE_TYPE:16},COMPILE_ERROR_CODES_EXTEND_POINT=17;function createCompileError(e,t,n={}){const{domain:r,messages:a,args:o}=n,s=new SyntaxError(String(e));return s.code=e,t&&(s.location=t),s.domain=r,s}function defaultOnError(e){throw e}const CHAR_SP=" ",CHAR_CR="\r",CHAR_LF="\n",CHAR_LS=String.fromCharCode(8232),CHAR_PS=String.fromCharCode(8233);function createScanner(e){const t=e;let n=0,r=1,a=1,o=0;const s=e=>t[e]===CHAR_CR&&t[e+1]===CHAR_LF,i=e=>t[e]===CHAR_PS,l=e=>t[e]===CHAR_LS,c=e=>s(e)||(e=>t[e]===CHAR_LF)(e)||i(e)||l(e),u=e=>s(e)||i(e)||l(e)?CHAR_LF:t[e];function m(){return o=0,c(n)&&(r++,a=0),s(n)&&n++,n++,a++,t[n]}return{index:()=>n,line:()=>r,column:()=>a,peekOffset:()=>o,charAt:u,currentChar:()=>u(n),currentPeek:()=>u(n+o),next:m,peek:function(){return s(n+o)&&o++,o++,t[n+o]},reset:function(){n=0,r=1,a=1,o=0},resetPeek:function(e=0){o=e},skipToPeek:function(){const e=n+o;for(;e!==n;)m();o=0}}}const EOF=void 0,DOT=".",LITERAL_DELIMITER="'",ERROR_DOMAIN$1="tokenizer";function createTokenizer(e,t={}){const n=!1!==t.location,r=createScanner(e),a=()=>r.index(),o=()=>createPosition(r.line(),r.column(),r.index()),s=o(),i=a(),l={currentType:13,offset:i,startLoc:s,endLoc:s,lastType:13,lastOffset:i,lastStartLoc:s,lastEndLoc:s,braceNest:0,inLinked:!1,text:""},c=()=>l,{onError:u}=t;function m(e,t,r){e.endLoc=o(),e.currentType=t;const a={type:t};return n&&(a.loc=createLocation(e.startLoc,e.endLoc)),null!=r&&(a.value=r),a}const f=e=>m(e,13);function p(e,t){return e.currentChar()===t?(e.next(),t):(CompileErrorCodes.EXPECTED_TOKEN,o(),"")}function _(e){let t="";for(;e.currentPeek()===CHAR_SP||e.currentPeek()===CHAR_LF;)t+=e.currentPeek(),e.peek();return t}function g(e){const t=_(e);return e.skipToPeek(),t}function d(e){if(e===EOF)return!1;const t=e.charCodeAt(0);return t>=97&&t<=122||t>=65&&t<=90||95===t}function E(e,t){const{currentType:n}=t;if(2!==n)return!1;_(e);const r=function(e){if(e===EOF)return!1;const t=e.charCodeAt(0);return t>=48&&t<=57}("-"===e.currentPeek()?e.peek():e.currentPeek());return e.resetPeek(),r}function b(e){_(e);const t="|"===e.currentPeek();return e.resetPeek(),t}function C(e,t=!0){const n=(t=!1,r="")=>{const a=e.currentPeek();return"{"===a?t:"@"!==a&&a?"|"===a?!(r===CHAR_SP||r===CHAR_LF):a===CHAR_SP?(e.peek(),n(!0,CHAR_SP)):a!==CHAR_LF||(e.peek(),n(!0,CHAR_LF)):t},r=n();return t&&e.resetPeek(),r}function O(e,t){const n=e.currentChar();return n===EOF?EOF:t(n)?(e.next(),n):null}function S(e){const t=e.charCodeAt(0);return t>=97&&t<=122||t>=65&&t<=90||t>=48&&t<=57||95===t||36===t}function h(e){return O(e,S)}function L(e){const t=e.charCodeAt(0);return t>=97&&t<=122||t>=65&&t<=90||t>=48&&t<=57||95===t||36===t||45===t}function T(e){return O(e,L)}function N(e){const t=e.charCodeAt(0);return t>=48&&t<=57}function k(e){return O(e,N)}function y(e){const t=e.charCodeAt(0);return t>=48&&t<=57||t>=65&&t<=70||t>=97&&t<=102}function I(e){return O(e,y)}function P(e){let t="",n="";for(;t=k(e);)n+=t;return n}function A(e){return e!==LITERAL_DELIMITER&&e!==CHAR_LF}function F(e){const t=e.currentChar();switch(t){case"\\":case"'":return e.next(),`\\${t}`;case"u":return R(e,t,4);case"U":return R(e,t,6);default:return CompileErrorCodes.UNKNOWN_ESCAPE_SEQUENCE,o(),""}}function R(e,t,n){p(e,t);let r="";for(let a=0;a<n;a++){const t=I(e);if(!t){CompileErrorCodes.INVALID_UNICODE_ESCAPE_SEQUENCE,o(),e.currentChar();break}r+=t}return`\\${t}${r}`}function v(e){return"{"!==e&&"}"!==e&&e!==CHAR_SP&&e!==CHAR_LF}function M(e){g(e);const t=p(e,"|");return g(e),t}function D(e,t){let n=null;switch(e.currentChar()){case"{":return t.braceNest>=1&&(CompileErrorCodes.NOT_ALLOW_NEST_PLACEHOLDER,o()),e.next(),n=m(t,2,"{"),g(e),t.braceNest++,n;case"}":return t.braceNest>0&&2===t.currentType&&(CompileErrorCodes.EMPTY_PLACEHOLDER,o()),e.next(),n=m(t,3,"}"),t.braceNest--,t.braceNest>0&&g(e),t.inLinked&&0===t.braceNest&&(t.inLinked=!1),n;case"@":return t.braceNest>0&&(CompileErrorCodes.UNTERMINATED_CLOSING_BRACE,o()),n=x(e,t)||f(t),t.braceNest=0,n;default:{let r=!0,a=!0,s=!0;if(b(e))return t.braceNest>0&&(CompileErrorCodes.UNTERMINATED_CLOSING_BRACE,o()),n=m(t,1,M(e)),t.braceNest=0,t.inLinked=!1,n;if(t.braceNest>0&&(4===t.currentType||5===t.currentType||6===t.currentType))return CompileErrorCodes.UNTERMINATED_CLOSING_BRACE,o(),t.braceNest=0,j(e,t);if(r=function(e,t){const{currentType:n}=t;if(2!==n)return!1;_(e);const r=d(e.currentPeek());return e.resetPeek(),r}(e,t))return n=m(t,4,function(e){g(e);let t="",n="";for(;t=T(e);)n+=t;return e.currentChar()===EOF&&(CompileErrorCodes.UNTERMINATED_CLOSING_BRACE,o()),n}(e)),g(e),n;if(a=E(e,t))return n=m(t,5,function(e){g(e);let t="";return"-"===e.currentChar()?(e.next(),t+=`-${P(e)}`):t+=P(e),e.currentChar()===EOF&&(CompileErrorCodes.UNTERMINATED_CLOSING_BRACE,o()),t}(e)),g(e),n;if(s=function(e,t){const{currentType:n}=t;if(2!==n)return!1;_(e);const r=e.currentPeek()===LITERAL_DELIMITER;return e.resetPeek(),r}(e,t))return n=m(t,6,function(e){g(e),p(e,"'");let t="",n="";for(;t=O(e,A);)n+="\\"===t?F(e):t;const r=e.currentChar();return r===CHAR_LF||r===EOF?(CompileErrorCodes.UNTERMINATED_SINGLE_QUOTE_IN_PLACEHOLDER,o(),r===CHAR_LF&&(e.next(),p(e,"'")),n):(p(e,"'"),n)}(e)),g(e),n;if(!r&&!a&&!s)return n=m(t,12,function(e){g(e);let t="",n="";for(;t=O(e,v);)n+=t;return n}(e)),CompileErrorCodes.INVALID_TOKEN_IN_PLACEHOLDER,o(),n.value,g(e),n;break}}return n}function x(e,t){const{currentType:n}=t;let r=null;const a=e.currentChar();switch(7!==n&&8!==n&&11!==n&&9!==n||a!==CHAR_LF&&a!==CHAR_SP||(CompileErrorCodes.INVALID_LINKED_FORMAT,o()),a){case"@":return e.next(),r=m(t,7,"@"),t.inLinked=!0,r;case".":return g(e),e.next(),m(t,8,".");case":":return g(e),e.next(),m(t,9,":");default:return b(e)?(r=m(t,1,M(e)),t.braceNest=0,t.inLinked=!1,r):function(e,t){const{currentType:n}=t;if(7!==n)return!1;_(e);const r="."===e.currentPeek();return e.resetPeek(),r}(e,t)||function(e,t){const{currentType:n}=t;if(7!==n&&11!==n)return!1;_(e);const r=":"===e.currentPeek();return e.resetPeek(),r}(e,t)?(g(e),x(e,t)):function(e,t){const{currentType:n}=t;if(8!==n)return!1;_(e);const r=d(e.currentPeek());return e.resetPeek(),r}(e,t)?(g(e),m(t,11,function(e){let t="",n="";for(;t=h(e);)n+=t;return n}(e))):function(e,t){const{currentType:n}=t;if(9!==n)return!1;const r=()=>{const t=e.currentPeek();return"{"===t?d(e.peek()):!("@"===t||"|"===t||":"===t||"."===t||t===CHAR_SP||!t)&&(t===CHAR_LF?(e.peek(),r()):C(e,!1))},a=r();return e.resetPeek(),a}(e,t)?(g(e),"{"===a?D(e,t)||r:m(t,10,function(e){const t=n=>{const r=e.currentChar();return"{"!==r&&"@"!==r&&"|"!==r&&"("!==r&&")"!==r&&r?r===CHAR_SP?n:(n+=r,e.next(),t(n)):n};return t("")}(e))):(7===n&&(CompileErrorCodes.INVALID_LINKED_FORMAT,o()),t.braceNest=0,t.inLinked=!1,j(e,t))}}function j(e,t){let n={type:13};if(t.braceNest>0)return D(e,t)||f(t);if(t.inLinked)return x(e,t)||f(t);switch(e.currentChar()){case"{":return D(e,t)||f(t);case"}":return CompileErrorCodes.UNBALANCED_CLOSING_BRACE,o(),e.next(),m(t,3,"}");case"@":return x(e,t)||f(t);default:if(b(e))return n=m(t,1,M(e)),t.braceNest=0,t.inLinked=!1,n;if(C(e))return m(t,0,function(e){let t="";for(;;){const n=e.currentChar();if("{"===n||"}"===n||"@"===n||"|"===n||!n)break;if(n===CHAR_SP||n===CHAR_LF)if(C(e))t+=n,e.next();else{if(b(e))break;t+=n,e.next()}else t+=n,e.next()}return t}(e))}return n}return{nextToken:function(){const{currentType:e,offset:t,startLoc:n,endLoc:s}=l;return l.lastType=e,l.lastOffset=t,l.lastStartLoc=n,l.lastEndLoc=s,l.offset=a(),l.startLoc=o(),r.currentChar()===EOF?m(l,13):j(r,l)},currentOffset:a,currentPosition:o,context:c}}const ERROR_DOMAIN="parser",KNOWN_ESCAPES=/(?:\\\\|\\'|\\u([0-9a-fA-F]{4})|\\U([0-9a-fA-F]{6}))/g;function fromEscapeSequence(e,t,n){switch(e){case"\\\\":return"\\";case"\\'":return"'";default:{const e=parseInt(t||n,16);return e<=55295||e>=57344?String.fromCodePoint(e):"�"}}}function createParser(e={}){const t=!1!==e.location,{onError:n}=e;function r(e,n,r){const a={type:e};return t&&(a.start=n,a.end=n,a.loc={start:r,end:r}),a}function a(e,n,r,a){t&&(e.end=n,e.loc&&(e.loc.end=r))}function o(e,t){const n=e.context(),o=r(3,n.offset,n.startLoc);return o.value=t,a(o,e.currentOffset(),e.currentPosition()),o}function s(e,t){const n=e.context(),{lastOffset:o,lastStartLoc:s}=n,i=r(5,o,s);return i.index=parseInt(t,10),e.nextToken(),a(i,e.currentOffset(),e.currentPosition()),i}function i(e,t){const n=e.context(),{lastOffset:o,lastStartLoc:s}=n,i=r(4,o,s);return i.key=t,e.nextToken(),a(i,e.currentOffset(),e.currentPosition()),i}function l(e,t){const n=e.context(),{lastOffset:o,lastStartLoc:s}=n,i=r(9,o,s);return i.value=t.replace(KNOWN_ESCAPES,fromEscapeSequence),e.nextToken(),a(i,e.currentOffset(),e.currentPosition()),i}function c(e){const t=e.context(),n=r(6,t.offset,t.startLoc);let o=e.nextToken();if(8===o.type){const t=function(e){const t=e.nextToken(),n=e.context(),{lastOffset:o,lastStartLoc:s}=n,i=r(8,o,s);return 11!==t.type?(CompileErrorCodes.UNEXPECTED_EMPTY_LINKED_MODIFIER,n.lastStartLoc,i.value="",a(i,o,s),{nextConsumeToken:t,node:i}):(null==t.value&&(CompileErrorCodes.UNEXPECTED_LEXICAL_ANALYSIS,n.lastStartLoc,getTokenCaption(t)),i.value=t.value||"",a(i,e.currentOffset(),e.currentPosition()),{node:i})}(e);n.modifier=t.node,o=t.nextConsumeToken||e.nextToken()}switch(9!==o.type&&(CompileErrorCodes.UNEXPECTED_LEXICAL_ANALYSIS,t.lastStartLoc,getTokenCaption(o)),o=e.nextToken(),2===o.type&&(o=e.nextToken()),o.type){case 10:null==o.value&&(CompileErrorCodes.UNEXPECTED_LEXICAL_ANALYSIS,t.lastStartLoc,getTokenCaption(o)),n.key=function(e,t){const n=e.context(),o=r(7,n.offset,n.startLoc);return o.value=t,a(o,e.currentOffset(),e.currentPosition()),o}(e,o.value||"");break;case 4:null==o.value&&(CompileErrorCodes.UNEXPECTED_LEXICAL_ANALYSIS,t.lastStartLoc,getTokenCaption(o)),n.key=i(e,o.value||"");break;case 5:null==o.value&&(CompileErrorCodes.UNEXPECTED_LEXICAL_ANALYSIS,t.lastStartLoc,getTokenCaption(o)),n.key=s(e,o.value||"");break;case 6:null==o.value&&(CompileErrorCodes.UNEXPECTED_LEXICAL_ANALYSIS,t.lastStartLoc,getTokenCaption(o)),n.key=l(e,o.value||"");break;default:{CompileErrorCodes.UNEXPECTED_EMPTY_LINKED_KEY,t.lastStartLoc;const s=e.context(),i=r(7,s.offset,s.startLoc);return i.value="",a(i,s.offset,s.startLoc),n.key=i,a(n,s.offset,s.startLoc),{nextConsumeToken:o,node:n}}}return a(n,e.currentOffset(),e.currentPosition()),{node:n}}function u(e){const t=e.context(),n=r(2,1===t.currentType?e.currentOffset():t.offset,1===t.currentType?t.endLoc:t.startLoc);n.items=[];let u=null;do{const r=u||e.nextToken();switch(u=null,r.type){case 0:null==r.value&&(CompileErrorCodes.UNEXPECTED_LEXICAL_ANALYSIS,t.lastStartLoc,getTokenCaption(r)),n.items.push(o(e,r.value||""));break;case 5:null==r.value&&(CompileErrorCodes.UNEXPECTED_LEXICAL_ANALYSIS,t.lastStartLoc,getTokenCaption(r)),n.items.push(s(e,r.value||""));break;case 4:null==r.value&&(CompileErrorCodes.UNEXPECTED_LEXICAL_ANALYSIS,t.lastStartLoc,getTokenCaption(r)),n.items.push(i(e,r.value||""));break;case 6:null==r.value&&(CompileErrorCodes.UNEXPECTED_LEXICAL_ANALYSIS,t.lastStartLoc,getTokenCaption(r)),n.items.push(l(e,r.value||""));break;case 7:{const t=c(e);n.items.push(t.node),u=t.nextConsumeToken||null;break}}}while(13!==t.currentType&&1!==t.currentType);return a(n,1===t.currentType?t.lastOffset:e.currentOffset(),1===t.currentType?t.lastEndLoc:e.currentPosition()),n}function m(e){const t=e.context(),{offset:n,startLoc:o}=t,s=u(e);return 13===t.currentType?s:function(e,t,n,o){const s=e.context();let i=0===o.items.length;const l=r(1,t,n);l.cases=[],l.cases.push(o);do{const t=u(e);i||(i=0===t.items.length),l.cases.push(t)}while(13!==s.currentType);return a(l,e.currentOffset(),e.currentPosition()),l}(e,n,o,s)}return{parse:function(n){const o=createTokenizer(n,assign({},e)),s=o.context(),i=r(0,s.offset,s.startLoc);return t&&i.loc&&(i.loc.source=n),i.body=m(o),e.onCacheKey&&(i.cacheKey=e.onCacheKey(n)),13!==s.currentType&&(CompileErrorCodes.UNEXPECTED_LEXICAL_ANALYSIS,s.lastStartLoc,n[s.offset]),a(i,o.currentOffset(),o.currentPosition()),i}}}function getTokenCaption(e){if(13===e.type)return"EOF";const t=(e.value||"").replace(/\r?\n/gu,"\\n");return t.length>10?t.slice(0,9)+"…":t}function createTransformer(e,t={}){const n={ast:e,helpers:new Set};return{context:()=>n,helper:e=>(n.helpers.add(e),e)}}function traverseNodes(e,t){for(let n=0;n<e.length;n++)traverseNode(e[n],t)}function traverseNode(e,t){switch(e.type){case 1:traverseNodes(e.cases,t),t.helper("plural");break;case 2:traverseNodes(e.items,t);break;case 6:traverseNode(e.key,t),t.helper("linked"),t.helper("type");break;case 5:t.helper("interpolate"),t.helper("list");break;case 4:t.helper("interpolate"),t.helper("named")}}function transform(e,t={}){const n=createTransformer(e);n.helper("normalize"),e.body&&traverseNode(e.body,n);const r=n.context();e.helpers=Array.from(r.helpers)}function optimize(e){const t=e.body;return 2===t.type?optimizeMessageNode(t):t.cases.forEach((e=>optimizeMessageNode(e))),e}function optimizeMessageNode(e){if(1===e.items.length){const t=e.items[0];3!==t.type&&9!==t.type||(e.static=t.value,delete t.value)}else{const t=[];for(let n=0;n<e.items.length;n++){const r=e.items[n];if(3!==r.type&&9!==r.type)break;if(null==r.value)break;t.push(r.value)}if(t.length===e.items.length){e.static=join(t);for(let t=0;t<e.items.length;t++){const n=e.items[t];3!==n.type&&9!==n.type||delete n.value}}}}function minify(e){switch(e.t=e.type,e.type){case 0:{const t=e;minify(t.body),t.b=t.body,delete t.body;break}case 1:{const t=e,n=t.cases;for(let e=0;e<n.length;e++)minify(n[e]);t.c=n,delete t.cases;break}case 2:{const t=e,n=t.items;for(let e=0;e<n.length;e++)minify(n[e]);t.i=n,delete t.items,t.static&&(t.s=t.static,delete t.static);break}case 3:case 9:case 8:case 7:{const t=e;t.value&&(t.v=t.value,delete t.value);break}case 6:{const t=e;minify(t.key),t.k=t.key,delete t.key,t.modifier&&(minify(t.modifier),t.m=t.modifier,delete t.modifier);break}case 5:{const t=e;t.i=t.index,delete t.index;break}case 4:{const t=e;t.k=t.key,delete t.key;break}}delete e.type}function createCodeGenerator(e,t){const{sourceMap:n,filename:r,breakLineCode:a,needIndent:o}=t,s=!1!==t.location,i={filename:r,code:"",column:1,line:1,offset:0,map:void 0,breakLineCode:a,needIndent:o,indentLevel:0};s&&e.loc&&(i.source=e.loc.source);function l(e,t){i.code+=e}function c(e,t=!0){const n=t?a:"";l(o?n+"  ".repeat(e):n)}return{context:()=>i,push:l,indent:function(e=!0){const t=++i.indentLevel;e&&c(t)},deindent:function(e=!0){const t=--i.indentLevel;e&&c(t)},newline:function(){c(i.indentLevel)},helper:e=>`_${e}`,needIndent:()=>i.needIndent}}function generateLinkedNode(e,t){const{helper:n}=e;e.push(`${n("linked")}(`),generateNode(e,t.key),t.modifier?(e.push(", "),generateNode(e,t.modifier),e.push(", _type")):e.push(", undefined, _type"),e.push(")")}function generateMessageNode(e,t){const{helper:n,needIndent:r}=e;e.push(`${n("normalize")}([`),e.indent(r());const a=t.items.length;for(let o=0;o<a&&(generateNode(e,t.items[o]),o!==a-1);o++)e.push(", ");e.deindent(r()),e.push("])")}function generatePluralNode(e,t){const{helper:n,needIndent:r}=e;if(t.cases.length>1){e.push(`${n("plural")}([`),e.indent(r());const a=t.cases.length;for(let n=0;n<a&&(generateNode(e,t.cases[n]),n!==a-1);n++)e.push(", ");e.deindent(r()),e.push("])")}}function generateResource(e,t){t.body?generateNode(e,t.body):e.push("null")}function generateNode(e,t){const{helper:n}=e;switch(t.type){case 0:generateResource(e,t);break;case 1:generatePluralNode(e,t);break;case 2:generateMessageNode(e,t);break;case 6:generateLinkedNode(e,t);break;case 8:case 7:case 9:case 3:e.push(JSON.stringify(t.value),t);break;case 5:e.push(`${n("interpolate")}(${n("list")}(${t.index}))`,t);break;case 4:e.push(`${n("interpolate")}(${n("named")}(${JSON.stringify(t.key)}))`,t)}}const generate=(e,t={})=>{const n=isString(t.mode)?t.mode:"normal",r=isString(t.filename)?t.filename:"message.intl",a=!!t.sourceMap,o=null!=t.breakLineCode?t.breakLineCode:"arrow"===n?";":"\n",s=t.needIndent?t.needIndent:"arrow"!==n,i=e.helpers||[],l=createCodeGenerator(e,{mode:n,filename:r,sourceMap:a,breakLineCode:o,needIndent:s});l.push("normal"===n?"function __msg__ (ctx) {":"(ctx) => {"),l.indent(s),i.length>0&&(l.push(`const { ${join(i.map((e=>`${e}: _${e}`)),", ")} } = ctx`),l.newline()),l.push("return "),generateNode(l,e),l.deindent(s),l.push("}"),delete e.helpers;const{code:c,map:u}=l.context();return{ast:e,code:c,map:u?u.toJSON():void 0}};function baseCompile$1(e,t={}){const n=assign({},t),r=!!n.jit,a=!!n.minify,o=null==n.optimize||n.optimize,s=createParser(n).parse(e);return r?(o&&optimize(s),a&&minify(s),{ast:s,code:""}):(transform(s,n),generate(s,n))}function format(e){return t=>formatParts(t,e)}function formatParts(e,t){const n=resolveBody(t);if(null==n)throw createUnhandleNodeError(0);if(1===resolveType(n)){const t=resolveCases(n);return e.plural(t.reduce(((t,n)=>[...t,formatMessageParts(e,n)]),[]))}return formatMessageParts(e,n)}const PROPS_BODY=["b","body"];function resolveBody(e){return resolveProps(e,PROPS_BODY)}const PROPS_CASES=["c","cases"];function resolveCases(e){return resolveProps(e,PROPS_CASES,[])}function formatMessageParts(e,t){const n=resolveStatic(t);if(null!=n)return"text"===e.type?n:e.normalize([n]);{const n=resolveItems(t).reduce(((t,n)=>[...t,formatMessagePart(e,n)]),[]);return e.normalize(n)}}const PROPS_STATIC=["s","static"];function resolveStatic(e){return resolveProps(e,PROPS_STATIC)}const PROPS_ITEMS=["i","items"];function resolveItems(e){return resolveProps(e,PROPS_ITEMS,[])}function formatMessagePart(e,t){const n=resolveType(t);switch(n){case 3:case 9:case 7:case 8:return resolveValue$1(t,n);case 4:{const r=t;if(hasOwn(r,"k")&&r.k)return e.interpolate(e.named(r.k));if(hasOwn(r,"key")&&r.key)return e.interpolate(e.named(r.key));throw createUnhandleNodeError(n)}case 5:{const r=t;if(hasOwn(r,"i")&&isNumber(r.i))return e.interpolate(e.list(r.i));if(hasOwn(r,"index")&&isNumber(r.index))return e.interpolate(e.list(r.index));throw createUnhandleNodeError(n)}case 6:{const n=t,r=resolveLinkedModifier(n),a=resolveLinkedKey(n);return e.linked(formatMessagePart(e,a),r?formatMessagePart(e,r):void 0,e.type)}default:throw new Error(`unhandled node on format message part: ${n}`)}}const PROPS_TYPE=["t","type"];function resolveType(e){return resolveProps(e,PROPS_TYPE)}const PROPS_VALUE=["v","value"];function resolveValue$1(e,t){const n=resolveProps(e,PROPS_VALUE);if(n)return n;throw createUnhandleNodeError(t)}const PROPS_MODIFIER=["m","modifier"];function resolveLinkedModifier(e){return resolveProps(e,PROPS_MODIFIER)}const PROPS_KEY=["k","key"];function resolveLinkedKey(e){const t=resolveProps(e,PROPS_KEY);if(t)return t;throw createUnhandleNodeError(6)}function resolveProps(e,t,n){for(let r=0;r<t.length;r++){const n=t[r];if(hasOwn(e,n)&&null!=e[n])return e[n]}return n}function createUnhandleNodeError(e){return new Error(`unhandled node type: ${e}`)}const defaultOnCacheKey=e=>e;let compileCache=create();function isMessageAST(e){return isObject(e)&&0===resolveType(e)&&(hasOwn(e,"b")||hasOwn(e,"body"))}function baseCompile(e,t={}){let n=!1;const r=t.onError||defaultOnError;return t.onError=e=>{n=!0,r(e)},{...baseCompile$1(e,t),detectError:n}}function compile(e,t){if(isString(e)){!isBoolean(t.warnHtmlMessage)||t.warnHtmlMessage;const n=(t.onCacheKey||defaultOnCacheKey)(e),r=compileCache[n];if(r)return r;const{ast:a,detectError:o}=baseCompile(e,{...t,location:!1,jit:!0}),s=format(a);return o?s:compileCache[n]=s}{const t=e.cacheKey;if(t){const n=compileCache[t];return n||(compileCache[t]=format(e))}return format(e)}}const CoreErrorCodes={INVALID_ARGUMENT:17,INVALID_DATE_ARGUMENT:18,INVALID_ISO_DATE_ARGUMENT:19,NOT_SUPPORT_NON_STRING_MESSAGE:20,NOT_SUPPORT_LOCALE_PROMISE_VALUE:21,NOT_SUPPORT_LOCALE_ASYNC_FUNCTION:22,NOT_SUPPORT_LOCALE_TYPE:23},CORE_ERROR_CODES_EXTEND_POINT=24;function getLocale(e,t){return null!=t.locale?resolveLocale(t.locale):resolveLocale(e.locale)}let _resolveLocale;function resolveLocale(e){if(isString(e))return e;if(isFunction(e)){if(e.resolvedOnce&&null!=_resolveLocale)return _resolveLocale;if("Function"===e.constructor.name){const t=e();if(isPromise(t))throw Error(CoreErrorCodes.NOT_SUPPORT_LOCALE_PROMISE_VALUE);return _resolveLocale=t}throw Error(CoreErrorCodes.NOT_SUPPORT_LOCALE_ASYNC_FUNCTION)}throw Error(CoreErrorCodes.NOT_SUPPORT_LOCALE_TYPE)}function fallbackWithSimple(e,t,n){return[...new Set([n,...isArray(t)?t:isObject(t)?Object.keys(t):isString(t)?[t]:[n]])]}function fallbackWithLocaleChain(e,t,n){const r=isString(n)?n:DEFAULT_LOCALE,a=e;a.__localeChainCache||(a.__localeChainCache=new Map);let o=a.__localeChainCache.get(r);if(!o){o=[];let e=[n];for(;isArray(e);)e=appendBlockToChain(o,e,t);const s=isArray(t)||!isPlainObject(t)?t:t.default?t.default:null;e=isString(s)?[s]:s,isArray(e)&&appendBlockToChain(o,e,!1),a.__localeChainCache.set(r,o)}return o}function appendBlockToChain(e,t,n){let r=!0;for(let a=0;a<t.length&&isBoolean(r);a++){const o=t[a];isString(o)&&(r=appendLocaleToChain(e,t[a],n))}return r}function appendLocaleToChain(e,t,n){let r;const a=t.split("-");do{r=appendItemToChain(e,a.join("-"),n),a.splice(-1,1)}while(a.length&&!0===r);return r}function appendItemToChain(e,t,n){let r=!1;if(!e.includes(t)&&(r=!0,t)){r="!"!==t[t.length-1];const a=t.replace(/!/g,"");e.push(a),(isArray(n)||isPlainObject(n))&&n[a]&&(r=n[a])}return r}const pathStateMachine=[];pathStateMachine[0]={w:[0],i:[3,0],"[":[4],o:[7]},pathStateMachine[1]={w:[1],".":[2],"[":[4],o:[7]},pathStateMachine[2]={w:[2],i:[3,0],0:[3,0]},pathStateMachine[3]={i:[3,0],0:[3,0],w:[1,1],".":[2,1],"[":[4,1],o:[7,1]},pathStateMachine[4]={"'":[5,0],'"':[6,0],"[":[4,2],"]":[1,3],o:8,l:[4,0]},pathStateMachine[5]={"'":[4,0],o:8,l:[5,0]},pathStateMachine[6]={'"':[4,0],o:8,l:[6,0]};const literalValueRE=/^\s?(?:true|false|-?[\d.]+|'[^']*'|"[^"]*")\s?$/;function isLiteral(e){return literalValueRE.test(e)}function stripQuotes(e){const t=e.charCodeAt(0);return t!==e.charCodeAt(e.length-1)||34!==t&&39!==t?e:e.slice(1,-1)}function getPathCharType(e){if(null==e)return"o";switch(e.charCodeAt(0)){case 91:case 93:case 46:case 34:case 39:return e;case 95:case 36:case 45:return"i";case 9:case 10:case 13:case 160:case 65279:case 8232:case 8233:return"w"}return"i"}function formatSubPath(e){const t=e.trim();return("0"!==e.charAt(0)||!isNaN(parseInt(e)))&&(isLiteral(t)?stripQuotes(t):"*"+t)}function parse(e){const t=[];let n,r,a,o,s,i,l,c=-1,u=0,m=0;const f=[];function p(){const t=e[c+1];if(5===u&&"'"===t||6===u&&'"'===t)return c++,a="\\"+t,f[0](),!0}for(f[0]=()=>{void 0===r?r=a:r+=a},f[1]=()=>{void 0!==r&&(t.push(r),r=void 0)},f[2]=()=>{f[0](),m++},f[3]=()=>{if(m>0)m--,u=4,f[0]();else{if(m=0,void 0===r)return!1;if(r=formatSubPath(r),!1===r)return!1;f[1]()}};null!==u;)if(c++,n=e[c],"\\"!==n||!p()){if(o=getPathCharType(n),l=pathStateMachine[u],s=l[o]||l.l||8,8===s)return;if(u=s[0],void 0!==s[1]&&(i=f[s[1]],i&&(a=n,!1===i())))return;if(7===u)return t}}const cache=new Map;function resolveWithKeyValue(e,t){return isObject(e)?e[t]:null}function resolveValue(e,t){if(!isObject(e))return null;let n=cache.get(t);if(n||(n=parse(t),n&&cache.set(t,n)),!n)return null;const r=n.length;let a=e,o=0;for(;o<r;){const e=a[n[o]];if(void 0===e)return null;if(isFunction(a))return null;a=e,o++}return a}const VERSION$1="11.1.2",NOT_REOSLVED=-1,DEFAULT_LOCALE="en-US",MISSING_RESOLVE_VALUE="",capitalize=e=>`${e.charAt(0).toLocaleUpperCase()}${e.substr(1)}`;function getDefaultLinkedModifiers(){return{upper:(e,t)=>"text"===t&&isString(e)?e.toUpperCase():"vnode"===t&&isObject(e)&&"__v_isVNode"in e?e.children.toUpperCase():e,lower:(e,t)=>"text"===t&&isString(e)?e.toLowerCase():"vnode"===t&&isObject(e)&&"__v_isVNode"in e?e.children.toLowerCase():e,capitalize:(e,t)=>"text"===t&&isString(e)?capitalize(e):"vnode"===t&&isObject(e)&&"__v_isVNode"in e?capitalize(e.children):e}}let _compiler,_resolver,_fallbacker;function registerMessageCompiler(e){_compiler=e}function registerMessageResolver(e){_resolver=e}function registerLocaleFallbacker(e){_fallbacker=e}const setAdditionalMeta=e=>{};let _fallbackContext=null;const setFallbackContext=e=>{_fallbackContext=e},getFallbackContext=()=>_fallbackContext;let _cid=0;function createCoreContext(e={}){const t=isFunction(e.onWarn)?e.onWarn:warn,n=isString(e.version)?e.version:VERSION$1,r=isString(e.locale)||isFunction(e.locale)?e.locale:DEFAULT_LOCALE,a=isFunction(r)?DEFAULT_LOCALE:r,o=isArray(e.fallbackLocale)||isPlainObject(e.fallbackLocale)||isString(e.fallbackLocale)||!1===e.fallbackLocale?e.fallbackLocale:a,s=isPlainObject(e.messages)?e.messages:createResources(a),i=isPlainObject(e.datetimeFormats)?e.datetimeFormats:createResources(a),l=isPlainObject(e.numberFormats)?e.numberFormats:createResources(a),c=assign(create(),e.modifiers,getDefaultLinkedModifiers()),u=e.pluralRules||create(),m=isFunction(e.missing)?e.missing:null,f=!isBoolean(e.missingWarn)&&!isRegExp(e.missingWarn)||e.missingWarn,p=!isBoolean(e.fallbackWarn)&&!isRegExp(e.fallbackWarn)||e.fallbackWarn,_=!!e.fallbackFormat,g=!!e.unresolving,d=isFunction(e.postTranslation)?e.postTranslation:null,E=isPlainObject(e.processor)?e.processor:null,b=!isBoolean(e.warnHtmlMessage)||e.warnHtmlMessage,C=!!e.escapeParameter,O=isFunction(e.messageCompiler)?e.messageCompiler:_compiler,S=isFunction(e.messageResolver)?e.messageResolver:_resolver||resolveWithKeyValue,h=isFunction(e.localeFallbacker)?e.localeFallbacker:_fallbacker||fallbackWithSimple,L=isObject(e.fallbackContext)?e.fallbackContext:void 0,T=e,N=isObject(T.__datetimeFormatters)?T.__datetimeFormatters:new Map,k=isObject(T.__numberFormatters)?T.__numberFormatters:new Map,y=isObject(T.__meta)?T.__meta:{};_cid++;const I={version:n,cid:_cid,locale:r,fallbackLocale:o,messages:s,modifiers:c,pluralRules:u,missing:m,missingWarn:f,fallbackWarn:p,fallbackFormat:_,unresolving:g,postTranslation:d,processor:E,warnHtmlMessage:b,escapeParameter:C,messageCompiler:O,messageResolver:S,localeFallbacker:h,fallbackContext:L,onWarn:t,__meta:y};return I.datetimeFormats=i,I.numberFormats=l,I.__datetimeFormatters=N,I.__numberFormatters=k,I}const createResources=e=>({[e]:create()});function handleMissing(e,t,n,r,a){const{missing:o,onWarn:s}=e;if(null!==o){const r=o(e,n,t,a);return isString(r)?r:t}return t}function updateFallbackLocale(e,t,n){e.__localeChainCache=new Map,e.localeFallbacker(e,n,t)}function isAlmostSameLocale(e,t){return e!==t&&e.split("-")[0]===t.split("-")[0]}function isImplicitFallback(e,t){const n=t.indexOf(e);if(-1===n)return!1;for(let r=n+1;r<t.length;r++)if(isAlmostSameLocale(e,t[r]))return!0;return!1}function datetime(e,...t){const{datetimeFormats:n,unresolving:r,fallbackLocale:a,onWarn:o,localeFallbacker:s}=e,{__datetimeFormatters:i}=e,[l,c,u,m]=parseDateTimeArgs(...t),f=isBoolean(u.missingWarn)?u.missingWarn:e.missingWarn;isBoolean(u.fallbackWarn)?u.fallbackWarn:e.fallbackWarn;const p=!!u.part,_=getLocale(e,u),g=s(e,a,_);if(!isString(l)||""===l)return new Intl.DateTimeFormat(_,m).format(c);let d,E={},b=null;for(let S=0;S<g.length&&(d=g[S],E=n[d]||{},b=E[l],!isPlainObject(b));S++)handleMissing(e,l,d,f,"datetime format");if(!isPlainObject(b)||!isString(d))return r?NOT_REOSLVED:l;let C=`${d}__${l}`;isEmptyObject(m)||(C=`${C}__${JSON.stringify(m)}`);let O=i.get(C);return O||(O=new Intl.DateTimeFormat(d,assign({},b,m)),i.set(C,O)),p?O.formatToParts(c):O.format(c)}const DATETIME_FORMAT_OPTIONS_KEYS=["localeMatcher","weekday","era","year","month","day","hour","minute","second","timeZoneName","formatMatcher","hour12","timeZone","dateStyle","timeStyle","calendar","dayPeriod","numberingSystem","hourCycle","fractionalSecondDigits"];function parseDateTimeArgs(...e){const[t,n,r,a]=e,o=create();let s,i=create();if(isString(t)){const e=t.match(/(\d{4}-\d{2}-\d{2})(T|\s)?(.*)/);if(!e)throw Error(CoreErrorCodes.INVALID_ISO_DATE_ARGUMENT);const n=e[3]?e[3].trim().startsWith("T")?`${e[1].trim()}${e[3].trim()}`:`${e[1].trim()}T${e[3].trim()}`:e[1].trim();s=new Date(n);try{s.toISOString()}catch{throw Error(CoreErrorCodes.INVALID_ISO_DATE_ARGUMENT)}}else if(isDate(t)){if(isNaN(t.getTime()))throw Error(CoreErrorCodes.INVALID_DATE_ARGUMENT);s=t}else{if(!isNumber(t))throw Error(CoreErrorCodes.INVALID_ARGUMENT);s=t}return isString(n)?o.key=n:isPlainObject(n)&&Object.keys(n).forEach((e=>{DATETIME_FORMAT_OPTIONS_KEYS.includes(e)?i[e]=n[e]:o[e]=n[e]})),isString(r)?o.locale=r:isPlainObject(r)&&(i=r),isPlainObject(a)&&(i=a),[o.key||"",s,o,i]}function clearDateTimeFormat(e,t,n){const r=e;for(const a in n){const e=`${t}__${a}`;r.__datetimeFormatters.has(e)&&r.__datetimeFormatters.delete(e)}}function number(e,...t){const{numberFormats:n,unresolving:r,fallbackLocale:a,onWarn:o,localeFallbacker:s}=e,{__numberFormatters:i}=e,[l,c,u,m]=parseNumberArgs(...t),f=isBoolean(u.missingWarn)?u.missingWarn:e.missingWarn;isBoolean(u.fallbackWarn)?u.fallbackWarn:e.fallbackWarn;const p=!!u.part,_=getLocale(e,u),g=s(e,a,_);if(!isString(l)||""===l)return new Intl.NumberFormat(_,m).format(c);let d,E={},b=null;for(let S=0;S<g.length&&(d=g[S],E=n[d]||{},b=E[l],!isPlainObject(b));S++)handleMissing(e,l,d,f,"number format");if(!isPlainObject(b)||!isString(d))return r?NOT_REOSLVED:l;let C=`${d}__${l}`;isEmptyObject(m)||(C=`${C}__${JSON.stringify(m)}`);let O=i.get(C);return O||(O=new Intl.NumberFormat(d,assign({},b,m)),i.set(C,O)),p?O.formatToParts(c):O.format(c)}const NUMBER_FORMAT_OPTIONS_KEYS=["localeMatcher","style","currency","currencyDisplay","currencySign","useGrouping","minimumIntegerDigits","minimumFractionDigits","maximumFractionDigits","minimumSignificantDigits","maximumSignificantDigits","compactDisplay","notation","signDisplay","unit","unitDisplay","roundingMode","roundingPriority","roundingIncrement","trailingZeroDisplay"];function parseNumberArgs(...e){const[t,n,r,a]=e,o=create();let s=create();if(!isNumber(t))throw Error(CoreErrorCodes.INVALID_ARGUMENT);const i=t;return isString(n)?o.key=n:isPlainObject(n)&&Object.keys(n).forEach((e=>{NUMBER_FORMAT_OPTIONS_KEYS.includes(e)?s[e]=n[e]:o[e]=n[e]})),isString(r)?o.locale=r:isPlainObject(r)&&(s=r),isPlainObject(a)&&(s=a),[o.key||"",i,o,s]}function clearNumberFormat(e,t,n){const r=e;for(const a in n){const e=`${t}__${a}`;r.__numberFormatters.has(e)&&r.__numberFormatters.delete(e)}}const DEFAULT_MODIFIER=e=>e,DEFAULT_MESSAGE=e=>"",DEFAULT_MESSAGE_DATA_TYPE="text",DEFAULT_NORMALIZE=e=>0===e.length?"":join(e),DEFAULT_INTERPOLATE=toDisplayString;function pluralDefault(e,t){return e=Math.abs(e),2===t?e?e>1?1:0:1:e?Math.min(e,2):0}function getPluralIndex(e){const t=isNumber(e.pluralIndex)?e.pluralIndex:-1;return e.named&&(isNumber(e.named.count)||isNumber(e.named.n))?isNumber(e.named.count)?e.named.count:isNumber(e.named.n)?e.named.n:t:t}function normalizeNamed(e,t){t.count||(t.count=e),t.n||(t.n=e)}function createMessageContext(e={}){const t=e.locale,n=getPluralIndex(e),r=isObject(e.pluralRules)&&isString(t)&&isFunction(e.pluralRules[t])?e.pluralRules[t]:pluralDefault,a=isObject(e.pluralRules)&&isString(t)&&isFunction(e.pluralRules[t])?pluralDefault:void 0,o=e.list||[],s=e.named||create();isNumber(e.pluralIndex)&&normalizeNamed(n,s);function i(t,n){const r=isFunction(e.messages)?e.messages(t,!!n):!!isObject(e.messages)&&e.messages[t];return r||(e.parent?e.parent.message(t):DEFAULT_MESSAGE)}const l=isPlainObject(e.processor)&&isFunction(e.processor.normalize)?e.processor.normalize:DEFAULT_NORMALIZE,c=isPlainObject(e.processor)&&isFunction(e.processor.interpolate)?e.processor.interpolate:DEFAULT_INTERPOLATE,u={list:e=>o[e],named:e=>s[e],plural:e=>e[r(n,e.length,a)],linked:(t,...n)=>{const[r,a]=n;let o="text",s="";1===n.length?isObject(r)?(s=r.modifier||s,o=r.type||o):isString(r)&&(s=r||s):2===n.length&&(isString(r)&&(s=r||s),isString(a)&&(o=a||o));const l=i(t,!0)(u),c="vnode"===o&&isArray(l)&&s?l[0]:l;return s?(m=s,e.modifiers?e.modifiers[m]:DEFAULT_MODIFIER)(c,o):c;var m},message:i,type:isPlainObject(e.processor)&&isString(e.processor.type)?e.processor.type:DEFAULT_MESSAGE_DATA_TYPE,interpolate:c,normalize:l,values:assign(create(),o,s)};return u}const NOOP_MESSAGE_FUNCTION=()=>"",isMessageFunction=e=>isFunction(e);function translate(e,...t){const{fallbackFormat:n,postTranslation:r,unresolving:a,messageCompiler:o,fallbackLocale:s,messages:i}=e,[l,c]=parseTranslateArgs(...t),u=isBoolean(c.missingWarn)?c.missingWarn:e.missingWarn,m=isBoolean(c.fallbackWarn)?c.fallbackWarn:e.fallbackWarn,f=isBoolean(c.escapeParameter)?c.escapeParameter:e.escapeParameter,p=!!c.resolvedMessage,_=isString(c.default)||isBoolean(c.default)?isBoolean(c.default)?o?l:()=>l:c.default:n?o?l:()=>l:null,g=n||null!=_&&(isString(_)||isFunction(_)),d=getLocale(e,c);f&&escapeParams(c);let[E,b,C]=p?[l,d,i[d]||create()]:resolveMessageFormat(e,l,d,s,m,u),O=E,S=l;if(p||isString(O)||isMessageAST(O)||isMessageFunction(O)||g&&(O=_,S=O),!(p||(isString(O)||isMessageAST(O)||isMessageFunction(O))&&isString(b)))return a?NOT_REOSLVED:l;let h=!1;const L=isMessageFunction(O)?O:compileMessageFormat(e,l,b,O,S,(()=>{h=!0}));if(h)return O;const T=evaluateMessage(e,L,createMessageContext(getMessageContextOptions(e,b,C,c)));return r?r(T,l):T}function escapeParams(e){isArray(e.list)?e.list=e.list.map((e=>isString(e)?escapeHtml(e):e)):isObject(e.named)&&Object.keys(e.named).forEach((t=>{isString(e.named[t])&&(e.named[t]=escapeHtml(e.named[t]))}))}function resolveMessageFormat(e,t,n,r,a,o){const{messages:s,onWarn:i,messageResolver:l,localeFallbacker:c}=e,u=c(e,r,n);let m,f=create(),p=null;for(let _=0;_<u.length&&(m=u[_],f=s[m]||create(),null===(p=l(f,t))&&(p=f[t]),!(isString(p)||isMessageAST(p)||isMessageFunction(p)));_++)if(!isImplicitFallback(m,u)){const n=handleMissing(e,t,m,o,"translate");n!==t&&(p=n)}return[p,m,f]}function compileMessageFormat(e,t,n,r,a,o){const{messageCompiler:s,warnHtmlMessage:i}=e;if(isMessageFunction(r)){const e=r;return e.locale=e.locale||n,e.key=e.key||t,e}if(null==s){const e=()=>r;return e.locale=n,e.key=t,e}const l=s(r,getCompileContext(e,n,a,r,i,o));return l.locale=n,l.key=t,l.source=r,l}function evaluateMessage(e,t,n){return t(n)}function parseTranslateArgs(...e){const[t,n,r]=e,a=create();if(!(isString(t)||isNumber(t)||isMessageFunction(t)||isMessageAST(t)))throw Error(CoreErrorCodes.INVALID_ARGUMENT);const o=isNumber(t)?String(t):(isMessageFunction(t),t);return isNumber(n)?a.plural=n:isString(n)?a.default=n:isPlainObject(n)&&!isEmptyObject(n)?a.named=n:isArray(n)&&(a.list=n),isNumber(r)?a.plural=r:isString(r)?a.default=r:isPlainObject(r)&&assign(a,r),[o,a]}function getCompileContext(e,t,n,r,a,o){return{locale:t,key:n,warnHtmlMessage:a,onError:e=>{throw o&&o(e),e},onCacheKey:e=>generateFormatCacheKey(t,n,e)}}function getMessageContextOptions(e,t,n,r){const{modifiers:a,pluralRules:o,messageResolver:s,fallbackLocale:i,fallbackWarn:l,missingWarn:c,fallbackContext:u}=e,m={locale:t,modifiers:a,pluralRules:o,messages:(r,a)=>{let o=s(n,r);if(null==o&&(u||a)){const[,,n]=resolveMessageFormat(u||e,r,t,i,l,c);o=s(n,r)}if(isString(o)||isMessageAST(o)){let n=!1;const a=compileMessageFormat(e,r,t,o,r,(()=>{n=!0}));return n?NOOP_MESSAGE_FUNCTION:a}return isMessageFunction(o)?o:NOOP_MESSAGE_FUNCTION}};return e.processor&&(m.processor=e.processor),r.list&&(m.list=r.list),r.named&&(m.named=r.named),isNumber(r.plural)&&(m.pluralIndex=r.plural),m}const VERSION="11.1.2",I18nErrorCodes={UNEXPECTED_RETURN_TYPE:24,INVALID_ARGUMENT:25,MUST_BE_CALL_SETUP_TOP:26,NOT_INSTALLED:27,REQUIRED_VALUE:28,INVALID_VALUE:29,CANNOT_SETUP_VUE_DEVTOOLS_PLUGIN:30,NOT_INSTALLED_WITH_PROVIDE:31,UNEXPECTED_ERROR:32,NOT_COMPATIBLE_LEGACY_VUE_I18N:33,NOT_AVAILABLE_COMPOSITION_IN_LEGACY:34},TranslateVNodeSymbol=makeSymbol("__translateVNode"),DatetimePartsSymbol=makeSymbol("__datetimeParts"),NumberPartsSymbol=makeSymbol("__numberParts"),SetPluralRulesSymbol=makeSymbol("__setPluralRules"),InejctWithOptionSymbol=makeSymbol("__injectWithOption"),DisposeSymbol=makeSymbol("__dispose");function handleFlatJson(e){if(!isObject(e))return e;for(const t in e)if(hasOwn(e,t))if(t.includes(".")){const n=t.split("."),r=n.length-1;let a=e,o=!1;for(let e=0;e<r;e++){if("__proto__"===n[e])throw new Error(`unsafe key: ${n[e]}`);if(n[e]in a||(a[n[e]]=create()),!isObject(a[n[e]])){o=!0;break}a=a[n[e]]}o||(a[n[r]]=e[t],delete e[t]),isObject(a[n[r]])&&handleFlatJson(a[n[r]])}else isObject(e[t])&&handleFlatJson(e[t]);return e}function getLocaleMessages(e,t){const{messages:n,__i18n:r,messageResolver:a,flatJson:o}=t,s=isPlainObject(n)?n:isArray(r)?create():{[e]:create()};if(isArray(r)&&r.forEach((e=>{if("locale"in e&&"resource"in e){const{locale:t,resource:n}=e;t?(s[t]=s[t]||create(),deepCopy(n,s[t])):deepCopy(n,s)}else isString(e)&&deepCopy(JSON.parse(e),s)})),null==a&&o)for(const i in s)hasOwn(s,i)&&handleFlatJson(s[i]);return s}function getComponentOptions(e){return e.type}function adjustI18nResources(e,t,n){let r=isObject(t.messages)?t.messages:create();"__i18nGlobal"in n&&(r=getLocaleMessages(e.locale.value,{messages:r,__i18n:n.__i18nGlobal}));const a=Object.keys(r);if(a.length&&a.forEach((t=>{e.mergeLocaleMessage(t,r[t])})),isObject(t.datetimeFormats)){const n=Object.keys(t.datetimeFormats);n.length&&n.forEach((n=>{e.mergeDateTimeFormat(n,t.datetimeFormats[n])}))}if(isObject(t.numberFormats)){const n=Object.keys(t.numberFormats);n.length&&n.forEach((n=>{e.mergeNumberFormat(n,t.numberFormats[n])}))}}function createTextNode(e){return createVNode(Text,null,e,0)}const DEVTOOLS_META="__INTLIFY_META__",NOOP_RETURN_ARRAY=()=>[],NOOP_RETURN_FALSE=()=>!1;let composerID=0;function defineCoreMissingHandler(e){return(t,n,r,a)=>e(n,r,getCurrentInstance()||void 0,a)}const getMetaInfo=()=>{const e=getCurrentInstance();let t=null;return e&&(t=getComponentOptions(e)[DEVTOOLS_META])?{[DEVTOOLS_META]:t}:null};function createComposer(e={}){const{__root:t,__injectWithOption:n}=e,r=void 0===t,a=e.flatJson,o=inBrowser?ref:shallowRef;let s=!isBoolean(e.inheritLocale)||e.inheritLocale;const i=o(t&&s?t.locale.value:isString(e.locale)?e.locale:DEFAULT_LOCALE),l=o(t&&s?t.fallbackLocale.value:isString(e.fallbackLocale)||isArray(e.fallbackLocale)||isPlainObject(e.fallbackLocale)||!1===e.fallbackLocale?e.fallbackLocale:i.value),c=o(getLocaleMessages(i.value,e)),u=o(isPlainObject(e.datetimeFormats)?e.datetimeFormats:{[i.value]:{}}),m=o(isPlainObject(e.numberFormats)?e.numberFormats:{[i.value]:{}});let f=t?t.missingWarn:!isBoolean(e.missingWarn)&&!isRegExp(e.missingWarn)||e.missingWarn,p=t?t.fallbackWarn:!isBoolean(e.fallbackWarn)&&!isRegExp(e.fallbackWarn)||e.fallbackWarn,_=t?t.fallbackRoot:!isBoolean(e.fallbackRoot)||e.fallbackRoot,g=!!e.fallbackFormat,d=isFunction(e.missing)?e.missing:null,E=isFunction(e.missing)?defineCoreMissingHandler(e.missing):null,b=isFunction(e.postTranslation)?e.postTranslation:null,C=t?t.warnHtmlMessage:!isBoolean(e.warnHtmlMessage)||e.warnHtmlMessage,O=!!e.escapeParameter;const S=t?t.modifiers:isPlainObject(e.modifiers)?e.modifiers:{};let h,L=e.pluralRules||t&&t.pluralRules;h=(()=>{r&&setFallbackContext(null);const t={version:VERSION,locale:i.value,fallbackLocale:l.value,messages:c.value,modifiers:S,pluralRules:L,missing:null===E?void 0:E,missingWarn:f,fallbackWarn:p,fallbackFormat:g,unresolving:!0,postTranslation:null===b?void 0:b,warnHtmlMessage:C,escapeParameter:O,messageResolver:e.messageResolver,messageCompiler:e.messageCompiler,__meta:{framework:"vue"}};t.datetimeFormats=u.value,t.numberFormats=m.value,t.__datetimeFormatters=isPlainObject(h)?h.__datetimeFormatters:void 0,t.__numberFormatters=isPlainObject(h)?h.__numberFormatters:void 0;const n=createCoreContext(t);return r&&setFallbackContext(n),n})(),updateFallbackLocale(h,i.value,l.value);const T=computed({get:()=>i.value,set:e=>{h.locale=e,i.value=e}}),N=computed({get:()=>l.value,set:e=>{h.fallbackLocale=e,l.value=e,updateFallbackLocale(h,i.value,e)}}),k=computed((()=>c.value)),y=computed((()=>u.value)),I=computed((()=>m.value));const P=(e,n,a,o,s,f)=>{let p;i.value,l.value,c.value,u.value,m.value;try{0,r||(h.fallbackContext=t?getFallbackContext():void 0),p=e(h)}finally{r||(h.fallbackContext=void 0)}if("translate exists"!==a&&isNumber(p)&&p===NOT_REOSLVED||"translate exists"===a&&!p){const[e,r]=n();return t&&_?o(t):s(e)}if(f(p))return p;throw Error(I18nErrorCodes.UNEXPECTED_RETURN_TYPE)};function A(...e){return P((t=>Reflect.apply(translate,null,[t,...e])),(()=>parseTranslateArgs(...e)),"translate",(t=>Reflect.apply(t.t,t,[...e])),(e=>e),(e=>isString(e)))}const F={normalize:function(e){return e.map((e=>isString(e)||isNumber(e)||isBoolean(e)?createTextNode(String(e)):e))},interpolate:e=>e,type:"vnode"};function R(e){return c.value[e]||{}}composerID++,t&&inBrowser&&(watch(t.locale,(e=>{s&&(i.value=e,h.locale=e,updateFallbackLocale(h,i.value,l.value))})),watch(t.fallbackLocale,(e=>{s&&(l.value=e,h.fallbackLocale=e,updateFallbackLocale(h,i.value,l.value))})));const v={id:composerID,locale:T,fallbackLocale:N,get inheritLocale(){return s},set inheritLocale(e){s=e,e&&t&&(i.value=t.locale.value,l.value=t.fallbackLocale.value,updateFallbackLocale(h,i.value,l.value))},get availableLocales(){return Object.keys(c.value).sort()},messages:k,get modifiers(){return S},get pluralRules(){return L||{}},get isGlobal(){return r},get missingWarn(){return f},set missingWarn(e){f=e,h.missingWarn=f},get fallbackWarn(){return p},set fallbackWarn(e){p=e,h.fallbackWarn=p},get fallbackRoot(){return _},set fallbackRoot(e){_=e},get fallbackFormat(){return g},set fallbackFormat(e){g=e,h.fallbackFormat=g},get warnHtmlMessage(){return C},set warnHtmlMessage(e){C=e,h.warnHtmlMessage=e},get escapeParameter(){return O},set escapeParameter(e){O=e,h.escapeParameter=e},t:A,getLocaleMessage:R,setLocaleMessage:function(e,t){if(a){const n={[e]:t};for(const e in n)hasOwn(n,e)&&handleFlatJson(n[e]);t=n[e]}c.value[e]=t,h.messages=c.value},mergeLocaleMessage:function(e,t){c.value[e]=c.value[e]||{};const n={[e]:t};if(a)for(const r in n)hasOwn(n,r)&&handleFlatJson(n[r]);deepCopy(t=n[e],c.value[e]),h.messages=c.value},getPostTranslationHandler:function(){return isFunction(b)?b:null},setPostTranslationHandler:function(e){b=e,h.postTranslation=e},getMissingHandler:function(){return d},setMissingHandler:function(e){null!==e&&(E=defineCoreMissingHandler(e)),d=e,h.missing=E},[SetPluralRulesSymbol]:function(e){L=e,h.pluralRules=L}};return v.datetimeFormats=y,v.numberFormats=I,v.rt=function(...e){const[t,n,r]=e;if(r&&!isObject(r))throw Error(I18nErrorCodes.INVALID_ARGUMENT);return A(t,n,assign({resolvedMessage:!0},r||{}))},v.te=function(e,t){return P((()=>{if(!e)return!1;const n=R(isString(t)?t:i.value),r=h.messageResolver(n,e);return isMessageAST(r)||isMessageFunction(r)||isString(r)}),(()=>[e]),"translate exists",(n=>Reflect.apply(n.te,n,[e,t])),NOOP_RETURN_FALSE,(e=>isBoolean(e)))},v.tm=function(e){const n=function(e){let t=null;const n=fallbackWithLocaleChain(h,l.value,i.value);for(let r=0;r<n.length;r++){const a=c.value[n[r]]||{},o=h.messageResolver(a,e);if(null!=o){t=o;break}}return t}(e);return null!=n?n:t&&t.tm(e)||{}},v.d=function(...e){return P((t=>Reflect.apply(datetime,null,[t,...e])),(()=>parseDateTimeArgs(...e)),"datetime format",(t=>Reflect.apply(t.d,t,[...e])),(()=>MISSING_RESOLVE_VALUE),(e=>isString(e)))},v.n=function(...e){return P((t=>Reflect.apply(number,null,[t,...e])),(()=>parseNumberArgs(...e)),"number format",(t=>Reflect.apply(t.n,t,[...e])),(()=>MISSING_RESOLVE_VALUE),(e=>isString(e)))},v.getDateTimeFormat=function(e){return u.value[e]||{}},v.setDateTimeFormat=function(e,t){u.value[e]=t,h.datetimeFormats=u.value,clearDateTimeFormat(h,e,t)},v.mergeDateTimeFormat=function(e,t){u.value[e]=assign(u.value[e]||{},t),h.datetimeFormats=u.value,clearDateTimeFormat(h,e,t)},v.getNumberFormat=function(e){return m.value[e]||{}},v.setNumberFormat=function(e,t){m.value[e]=t,h.numberFormats=m.value,clearNumberFormat(h,e,t)},v.mergeNumberFormat=function(e,t){m.value[e]=assign(m.value[e]||{},t),h.numberFormats=m.value,clearNumberFormat(h,e,t)},v[InejctWithOptionSymbol]=n,v[TranslateVNodeSymbol]=function(...e){return P((t=>{let n;const r=t;try{r.processor=F,n=Reflect.apply(translate,null,[r,...e])}finally{r.processor=null}return n}),(()=>parseTranslateArgs(...e)),"translate",(t=>t[TranslateVNodeSymbol](...e)),(e=>[createTextNode(e)]),(e=>isArray(e)))},v[DatetimePartsSymbol]=function(...e){return P((t=>Reflect.apply(datetime,null,[t,...e])),(()=>parseDateTimeArgs(...e)),"datetime format",(t=>t[DatetimePartsSymbol](...e)),NOOP_RETURN_ARRAY,(e=>isString(e)||isArray(e)))},v[NumberPartsSymbol]=function(...e){return P((t=>Reflect.apply(number,null,[t,...e])),(()=>parseNumberArgs(...e)),"number format",(t=>t[NumberPartsSymbol](...e)),NOOP_RETURN_ARRAY,(e=>isString(e)||isArray(e)))},v}function convertComposerOptions(e){const t=isString(e.locale)?e.locale:DEFAULT_LOCALE,n=isString(e.fallbackLocale)||isArray(e.fallbackLocale)||isPlainObject(e.fallbackLocale)||!1===e.fallbackLocale?e.fallbackLocale:t,r=isFunction(e.missing)?e.missing:void 0,a=!isBoolean(e.silentTranslationWarn)&&!isRegExp(e.silentTranslationWarn)||!e.silentTranslationWarn,o=!isBoolean(e.silentFallbackWarn)&&!isRegExp(e.silentFallbackWarn)||!e.silentFallbackWarn,s=!isBoolean(e.fallbackRoot)||e.fallbackRoot,i=!!e.formatFallbackMessages,l=isPlainObject(e.modifiers)?e.modifiers:{},c=e.pluralizationRules,u=isFunction(e.postTranslation)?e.postTranslation:void 0,m=!isString(e.warnHtmlInMessage)||"off"!==e.warnHtmlInMessage,f=!!e.escapeParameterHtml,p=!isBoolean(e.sync)||e.sync;let _=e.messages;if(isPlainObject(e.sharedMessages)){const t=e.sharedMessages;_=Object.keys(t).reduce(((e,n)=>{const r=e[n]||(e[n]={});return assign(r,t[n]),e}),_||{})}const{__i18n:g,__root:d,__injectWithOption:E}=e,b=e.datetimeFormats,C=e.numberFormats;return{locale:t,fallbackLocale:n,messages:_,flatJson:e.flatJson,datetimeFormats:b,numberFormats:C,missing:r,missingWarn:a,fallbackWarn:o,fallbackRoot:s,fallbackFormat:i,modifiers:l,pluralRules:c,postTranslation:u,warnHtmlMessage:m,escapeParameter:f,messageResolver:e.messageResolver,inheritLocale:p,__i18n:g,__root:d,__injectWithOption:E}}function createVueI18n(e={}){const t=createComposer(convertComposerOptions(e)),{__extender:n}=e,r={id:t.id,get locale(){return t.locale.value},set locale(e){t.locale.value=e},get fallbackLocale(){return t.fallbackLocale.value},set fallbackLocale(e){t.fallbackLocale.value=e},get messages(){return t.messages.value},get datetimeFormats(){return t.datetimeFormats.value},get numberFormats(){return t.numberFormats.value},get availableLocales(){return t.availableLocales},get missing(){return t.getMissingHandler()},set missing(e){t.setMissingHandler(e)},get silentTranslationWarn(){return isBoolean(t.missingWarn)?!t.missingWarn:t.missingWarn},set silentTranslationWarn(e){t.missingWarn=isBoolean(e)?!e:e},get silentFallbackWarn(){return isBoolean(t.fallbackWarn)?!t.fallbackWarn:t.fallbackWarn},set silentFallbackWarn(e){t.fallbackWarn=isBoolean(e)?!e:e},get modifiers(){return t.modifiers},get formatFallbackMessages(){return t.fallbackFormat},set formatFallbackMessages(e){t.fallbackFormat=e},get postTranslation(){return t.getPostTranslationHandler()},set postTranslation(e){t.setPostTranslationHandler(e)},get sync(){return t.inheritLocale},set sync(e){t.inheritLocale=e},get warnHtmlInMessage(){return t.warnHtmlMessage?"warn":"off"},set warnHtmlInMessage(e){t.warnHtmlMessage="off"!==e},get escapeParameterHtml(){return t.escapeParameter},set escapeParameterHtml(e){t.escapeParameter=e},get pluralizationRules(){return t.pluralRules||{}},__composer:t,t:(...e)=>Reflect.apply(t.t,t,[...e]),rt:(...e)=>Reflect.apply(t.rt,t,[...e]),te:(e,n)=>t.te(e,n),tm:e=>t.tm(e),getLocaleMessage:e=>t.getLocaleMessage(e),setLocaleMessage(e,n){t.setLocaleMessage(e,n)},mergeLocaleMessage(e,n){t.mergeLocaleMessage(e,n)},d:(...e)=>Reflect.apply(t.d,t,[...e]),getDateTimeFormat:e=>t.getDateTimeFormat(e),setDateTimeFormat(e,n){t.setDateTimeFormat(e,n)},mergeDateTimeFormat(e,n){t.mergeDateTimeFormat(e,n)},n:(...e)=>Reflect.apply(t.n,t,[...e]),getNumberFormat:e=>t.getNumberFormat(e),setNumberFormat(e,n){t.setNumberFormat(e,n)},mergeNumberFormat(e,n){t.mergeNumberFormat(e,n)}};return r.__extender=n,r}function defineMixin(e,t,n){return{beforeCreate(){const r=getCurrentInstance();if(!r)throw Error(I18nErrorCodes.UNEXPECTED_ERROR);const a=this.$options;if(a.i18n){const r=a.i18n;if(a.__i18n&&(r.__i18n=a.__i18n),r.__root=t,this===this.$root)this.$i18n=mergeToGlobal(e,r);else{r.__injectWithOption=!0,r.__extender=n.__vueI18nExtend,this.$i18n=createVueI18n(r);const e=this.$i18n;e.__extender&&(e.__disposer=e.__extender(this.$i18n))}}else if(a.__i18n)if(this===this.$root)this.$i18n=mergeToGlobal(e,a);else{this.$i18n=createVueI18n({__i18n:a.__i18n,__injectWithOption:!0,__extender:n.__vueI18nExtend,__root:t});const e=this.$i18n;e.__extender&&(e.__disposer=e.__extender(this.$i18n))}else this.$i18n=e;a.__i18nGlobal&&adjustI18nResources(t,a,a),this.$t=(...e)=>this.$i18n.t(...e),this.$rt=(...e)=>this.$i18n.rt(...e),this.$te=(e,t)=>this.$i18n.te(e,t),this.$d=(...e)=>this.$i18n.d(...e),this.$n=(...e)=>this.$i18n.n(...e),this.$tm=e=>this.$i18n.tm(e),n.__setInstance(r,this.$i18n)},mounted(){},unmounted(){const e=getCurrentInstance();if(!e)throw Error(I18nErrorCodes.UNEXPECTED_ERROR);const t=this.$i18n;delete this.$t,delete this.$rt,delete this.$te,delete this.$d,delete this.$n,delete this.$tm,t.__disposer&&(t.__disposer(),delete t.__disposer,delete t.__extender),n.__deleteInstance(e),delete this.$i18n}}}function mergeToGlobal(e,t){e.locale=t.locale||e.locale,e.fallbackLocale=t.fallbackLocale||e.fallbackLocale,e.missing=t.missing||e.missing,e.silentTranslationWarn=t.silentTranslationWarn||e.silentFallbackWarn,e.silentFallbackWarn=t.silentFallbackWarn||e.silentFallbackWarn,e.formatFallbackMessages=t.formatFallbackMessages||e.formatFallbackMessages,e.postTranslation=t.postTranslation||e.postTranslation,e.warnHtmlInMessage=t.warnHtmlInMessage||e.warnHtmlInMessage,e.escapeParameterHtml=t.escapeParameterHtml||e.escapeParameterHtml,e.sync=t.sync||e.sync,e.__composer[SetPluralRulesSymbol](t.pluralizationRules||e.pluralizationRules);const n=getLocaleMessages(e.locale,{messages:t.messages,__i18n:t.__i18n});return Object.keys(n).forEach((t=>e.mergeLocaleMessage(t,n[t]))),t.datetimeFormats&&Object.keys(t.datetimeFormats).forEach((n=>e.mergeDateTimeFormat(n,t.datetimeFormats[n]))),t.numberFormats&&Object.keys(t.numberFormats).forEach((n=>e.mergeNumberFormat(n,t.numberFormats[n]))),e}const baseFormatProps={tag:{type:[String,Object]},locale:{type:String},scope:{type:String,validator:e=>"parent"===e||"global"===e,default:"parent"},i18n:{type:Object}};function getInterpolateArg({slots:e},t){if(1===t.length&&"default"===t[0]){return(e.default?e.default():[]).reduce(((e,t)=>[...e,...t.type===Fragment?t.children:[t]]),[])}return t.reduce(((t,n)=>{const r=e[n];return r&&(t[n]=r()),t}),create())}function getFragmentableTag(){return Fragment}const TranslationImpl=defineComponent({name:"i18n-t",props:assign({keypath:{type:String,required:!0},plural:{type:[Number,String],validator:e=>isNumber(e)||!isNaN(e)}},baseFormatProps),setup(e,t){const{slots:n,attrs:r}=t,a=e.i18n||useI18n({useScope:e.scope,__useComponent:!0});return()=>{const o=Object.keys(n).filter((e=>"_"!==e)),s=create();e.locale&&(s.locale=e.locale),void 0!==e.plural&&(s.plural=isString(e.plural)?+e.plural:e.plural);const i=getInterpolateArg(t,o),l=a[TranslateVNodeSymbol](e.keypath,i,s),c=assign(create(),r),u=isString(e.tag)||isObject(e.tag)?e.tag:getFragmentableTag();return h(u,c,l)}}}),Translation=TranslationImpl,I18nT=Translation;function isVNode(e){return isArray(e)&&!isString(e[0])}function renderFormatter(e,t,n,r){const{slots:a,attrs:o}=t;return()=>{const t={part:!0};let s=create();e.locale&&(t.locale=e.locale),isString(e.format)?t.key=e.format:isObject(e.format)&&(isString(e.format.key)&&(t.key=e.format.key),s=Object.keys(e.format).reduce(((t,r)=>n.includes(r)?assign(create(),t,{[r]:e.format[r]}):t),create()));const i=r(e.value,t,s);let l=[t.key];isArray(i)?l=i.map(((e,t)=>{const n=a[e.type],r=n?n({[e.type]:e.value,index:t,parts:i}):[e.value];return isVNode(r)&&(r[0].key=`${e.type}-${t}`),r})):isString(i)&&(l=[i]);const c=assign(create(),o),u=isString(e.tag)||isObject(e.tag)?e.tag:getFragmentableTag();return h(u,c,l)}}const NumberFormatImpl=defineComponent({name:"i18n-n",props:assign({value:{type:Number,required:!0},format:{type:[String,Object]}},baseFormatProps),setup(e,t){const n=e.i18n||useI18n({useScope:e.scope,__useComponent:!0});return renderFormatter(e,t,NUMBER_FORMAT_OPTIONS_KEYS,((...e)=>n[NumberPartsSymbol](...e)))}}),NumberFormat=NumberFormatImpl,I18nN=NumberFormat;function getComposer$1(e,t){const n=e;if("composition"===e.mode)return n.__getInstance(t)||e.global;{const r=n.__getInstance(t);return null!=r?r.__composer:e.global.__composer}}function vTDirective(e){const t=t=>{const{instance:n,value:r}=t;if(!n||!n.$)throw Error(I18nErrorCodes.UNEXPECTED_ERROR);const a=getComposer$1(e,n.$),o=parseValue(r);return[Reflect.apply(a.t,a,[...makeParams(o)]),a]};return{created:(n,r)=>{const[a,o]=t(r);inBrowser&&e.global===o&&(n.__i18nWatcher=watch(o.locale,(()=>{r.instance&&r.instance.$forceUpdate()}))),n.__composer=o,n.textContent=a},unmounted:e=>{inBrowser&&e.__i18nWatcher&&(e.__i18nWatcher(),e.__i18nWatcher=void 0,delete e.__i18nWatcher),e.__composer&&(e.__composer=void 0,delete e.__composer)},beforeUpdate:(e,{value:t})=>{if(e.__composer){const n=e.__composer,r=parseValue(t);e.textContent=Reflect.apply(n.t,n,[...makeParams(r)])}},getSSRProps:e=>{const[n]=t(e);return{textContent:n}}}}function parseValue(e){if(isString(e))return{path:e};if(isPlainObject(e)){if(!("path"in e))throw Error(I18nErrorCodes.REQUIRED_VALUE,"path");return e}throw Error(I18nErrorCodes.INVALID_VALUE)}function makeParams(e){const{path:t,locale:n,args:r,choice:a,plural:o}=e,s={},i=r||{};return isString(n)&&(s.locale=n),isNumber(a)&&(s.plural=a),isNumber(o)&&(s.plural=o),[t,i,s]}function apply(e,t,...n){const r=isPlainObject(n[0])?n[0]:{};(!isBoolean(r.globalInstall)||r.globalInstall)&&([Translation.name,"I18nT"].forEach((t=>e.component(t,Translation))),[NumberFormat.name,"I18nN"].forEach((t=>e.component(t,NumberFormat))),[DatetimeFormat.name,"I18nD"].forEach((t=>e.component(t,DatetimeFormat)))),e.directive("t",vTDirective(t))}const I18nInjectionKey=makeSymbol("global-vue-i18n");function createI18n(e={}){const t=!isBoolean(e.legacy)||e.legacy,n=!isBoolean(e.globalInjection)||e.globalInjection,r=new Map,[a,o]=createGlobal(e,t),s=makeSymbol("");const i={get mode(){return t?"legacy":"composition"},async install(e,...r){if(e.__VUE_I18N_SYMBOL__=s,e.provide(e.__VUE_I18N_SYMBOL__,i),isPlainObject(r[0])){const e=r[0];i.__composerExtend=e.__composerExtend,i.__vueI18nExtend=e.__vueI18nExtend}let a=null;!t&&n&&(a=injectGlobalFields(e,i.global)),apply(e,i,...r),t&&e.mixin(defineMixin(o,o.__composer,i));const l=e.unmount;e.unmount=()=>{a&&a(),i.dispose(),l()}},get global(){return o},dispose(){a.stop()},__instances:r,__getInstance:function(e){return r.get(e)||null},__setInstance:function(e,t){r.set(e,t)},__deleteInstance:function(e){r.delete(e)}};return i}function useI18n(e={}){const t=getCurrentInstance();if(null==t)throw Error(I18nErrorCodes.MUST_BE_CALL_SETUP_TOP);if(!t.isCE&&null!=t.appContext.app&&!t.appContext.app.__VUE_I18N_SYMBOL__)throw Error(I18nErrorCodes.NOT_INSTALLED);const n=getI18nInstance(t),r=getGlobalComposer(n),a=getComponentOptions(t),o=getScope(e,a);if("global"===o)return adjustI18nResources(r,e,a),r;if("parent"===o){let a=getComposer(n,t,e.__useComponent);return null==a&&(a=r),a}const s=n;let i=s.__getInstance(t);if(null==i){const n=assign({},e);"__i18n"in a&&(n.__i18n=a.__i18n),r&&(n.__root=r),i=createComposer(n),s.__composerExtend&&(i[DisposeSymbol]=s.__composerExtend(i)),setupLifeCycle(s,t,i),s.__setInstance(t,i)}return i}function createGlobal(e,t){const n=effectScope(),r=t?n.run((()=>createVueI18n(e))):n.run((()=>createComposer(e)));if(null==r)throw Error(I18nErrorCodes.UNEXPECTED_ERROR);return[n,r]}function getI18nInstance(e){const t=inject(e.isCE?I18nInjectionKey:e.appContext.app.__VUE_I18N_SYMBOL__);if(!t)throw Error(e.isCE?I18nErrorCodes.NOT_INSTALLED_WITH_PROVIDE:I18nErrorCodes.UNEXPECTED_ERROR);return t}function getScope(e,t){return isEmptyObject(e)?"__i18n"in t?"local":"global":e.useScope?e.useScope:"local"}function getGlobalComposer(e){return"composition"===e.mode?e.global:e.global.__composer}function getComposer(e,t,n=!1){let r=null;const a=t.root;let o=getParentComponentInstance(t,n);for(;null!=o;){const t=e;if("composition"===e.mode)r=t.__getInstance(o);else{const e=t.__getInstance(o);null!=e&&(r=e.__composer,n&&r&&!r[InejctWithOptionSymbol]&&(r=null))}if(null!=r)break;if(a===o)break;o=o.parent}return r}function getParentComponentInstance(e,t=!1){return null==e?null:t&&e.vnode.ctx||e.parent}function setupLifeCycle(e,t,n){onMounted((()=>{}),t),onUnmounted((()=>{const r=n;e.__deleteInstance(t);const a=r[DisposeSymbol];a&&(a(),delete r[DisposeSymbol])}),t)}const globalExportProps=["locale","fallbackLocale","availableLocales"],globalExportMethods=["t","rt","d","n","tm","te"];function injectGlobalFields(e,t){const n=Object.create(null);globalExportProps.forEach((e=>{const r=Object.getOwnPropertyDescriptor(t,e);if(!r)throw Error(I18nErrorCodes.UNEXPECTED_ERROR);const a=isRef(r.value)?{get:()=>r.value.value,set(e){r.value.value=e}}:{get:()=>r.get&&r.get()};Object.defineProperty(n,e,a)})),e.config.globalProperties.$i18n=n,globalExportMethods.forEach((n=>{const r=Object.getOwnPropertyDescriptor(t,n);if(!r||!r.value)throw Error(I18nErrorCodes.UNEXPECTED_ERROR);Object.defineProperty(e.config.globalProperties,`$${n}`,r)}));return()=>{delete e.config.globalProperties.$i18n,globalExportMethods.forEach((t=>{delete e.config.globalProperties[`$${t}`]}))}}const DatetimeFormatImpl=defineComponent({name:"i18n-d",props:assign({value:{type:[Number,Date],required:!0},format:{type:[String,Object]}},baseFormatProps),setup(e,t){const n=e.i18n||useI18n({useScope:e.scope,__useComponent:!0});return renderFormatter(e,t,DATETIME_FORMAT_OPTIONS_KEYS,((...e)=>n[DatetimePartsSymbol](...e)))}}),DatetimeFormat=DatetimeFormatImpl,I18nD=DatetimeFormat;registerMessageCompiler(compile),registerMessageResolver(resolveValue),registerLocaleFallbacker(fallbackWithLocaleChain);export{DatetimeFormat,I18nD,I18nInjectionKey,I18nN,I18nT,NumberFormat,Translation,VERSION,createI18n,useI18n,vTDirective};
