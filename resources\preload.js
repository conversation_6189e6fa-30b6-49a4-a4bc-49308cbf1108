const { app, contextBridge, ipc<PERSON><PERSON><PERSON> } = require('electron');
const fs = require('fs');
const path = require('path');

let currentPath = ipcRenderer.sendSync('FilePath');
let videoName = 'index'
const lastIndex = currentPath.lastIndexOf('\\');
currentPath = currentPath.substring(0, lastIndex);
if (process.env.NODE_ENV !== 'development') {
  const Index = currentPath.lastIndexOf('\\');
  currentPath = currentPath.substring(0, Index);
}
if (currentPath.startsWith('C:')) {
  currentPath = "C:\\"
}
contextBridge.exposeInMainWorld('electronAPI', {
  on: (channel, func) => {
    // 显式地列出允许监听的频道
    const validChannels = ['message-from-main', 'openScreen', "receiveMonitor", "sendVideo", 'setClock', "keycode"];
    if (validChannels.includes(channel)) {
      ipcRenderer.on(channel, (event, ...args) => func(...args));
    }
  },
  sendMessageToMain: (message) => {
    videoName = message.name
    ipcRenderer.send('closeWindow', message.type);
  },
  sendMessageToMain1: async (message) => {

    const outputFilePath = path.join(currentPath + '\\Wallpaper_Download\\videoStorage', videoName + '.mp4');

    const buffer = await message.arrayBuffer();
    // 将视频数据写入文件系统
    fs.writeFile(outputFilePath, Buffer.from(buffer), (err) => {
      if (err) {
      } else {
        ipcRenderer.send('generationSuccess', outputFilePath);
      }
    });

  },
  sendIdentifier: (Identifier) => {
    console.log(Identifier);

    ipcRenderer.send('onIdentifier', Identifier);
  },

  // 可以暴露其他API，但需要确保安全
});
