{"name": "electron-as-wallpaper", "version": "2.0.3", "author": {"name": "meslzy", "email": "<EMAIL>", "url": "https://meslzy.com/"}, "description": "set your electron window as wallpaper behind desktop icons", "repository": "github:meslzy/electron-as-wallpaper", "homepage": "https://github.com/meslzy/electron-as-wallpaper", "publishConfig": {"access": "public"}, "types": "dist/main.d.ts", "main": "dist/main.cjs", "module": "dist/main.js", "exports": {".": {"import": "./dist/main.js", "require": "./dist/main.cjs"}}, "type": "module", "dependencies": {"bindings": "^1.5.0", "cargo-cp-artifact": "^0.1.9"}, "devDependencies": {"@meslzy/eslint": "^2.0.0", "@meslzy/tsconfig": "^0.2.8", "@types/bindings": "^1.5.5", "@types/node": "^22.10.4", "electron": "^33.2.1", "eslint": "^9.17.0", "tsup": "^8.3.5", "typescript": "^5.7.2"}, "files": ["dist", "src", "Cargo.toml", "license"], "license": "mit"}