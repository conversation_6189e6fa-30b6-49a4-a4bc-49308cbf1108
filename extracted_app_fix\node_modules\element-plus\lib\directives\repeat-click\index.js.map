{"version": 3, "file": "index.js", "sources": ["../../../../../packages/directives/repeat-click/index.ts"], "sourcesContent": ["import { isFunction } from '@element-plus/utils'\n\nimport type { ObjectDirective } from 'vue'\n\nexport const REPEAT_INTERVAL = 100\nexport const REPEAT_DELAY = 600\n\nexport interface RepeatClickOptions {\n  interval?: number\n  delay?: number\n  handler: (...args: unknown[]) => unknown\n}\n\nexport const vRepeatClick: ObjectDirective<\n  HTMLElement,\n  RepeatClickOptions | RepeatClickOptions['handler']\n> = {\n  beforeMount(el, binding) {\n    const value = binding.value\n    const { interval = REPEAT_INTERVAL, delay = REPEAT_DELAY } = isFunction(\n      value\n    )\n      ? {}\n      : value\n\n    let intervalId: ReturnType<typeof setInterval> | undefined\n    let delayId: ReturnType<typeof setTimeout> | undefined\n\n    const handler = () => (isFunction(value) ? value() : value.handler())\n\n    const clear = () => {\n      if (delayId) {\n        clearTimeout(delayId)\n        delayId = undefined\n      }\n      if (intervalId) {\n        clearInterval(intervalId)\n        intervalId = undefined\n      }\n    }\n\n    el.addEventListener('mousedown', (evt: MouseEvent) => {\n      if (evt.button !== 0) return\n      clear()\n      handler()\n\n      document.addEventListener('mouseup', () => clear(), {\n        once: true,\n      })\n\n      delayId = setTimeout(() => {\n        intervalId = setInterval(() => {\n          handler()\n        }, interval)\n      }, delay)\n    })\n  },\n}\n"], "names": ["isFunction"], "mappings": ";;;;;;AACY,MAAC,eAAe,GAAG,IAAI;AACvB,MAAC,YAAY,GAAG,IAAI;AACpB,MAAC,YAAY,GAAG;AAC5B,EAAE,WAAW,CAAC,EAAE,EAAE,OAAO,EAAE;AAC3B,IAAI,MAAM,KAAK,GAAG,OAAO,CAAC,KAAK,CAAC;AAChC,IAAI,MAAM,EAAE,QAAQ,GAAG,eAAe,EAAE,KAAK,GAAG,YAAY,EAAE,GAAGA,iBAAU,CAAC,KAAK,CAAC,GAAG,EAAE,GAAG,KAAK,CAAC;AAChG,IAAI,IAAI,UAAU,CAAC;AACnB,IAAI,IAAI,OAAO,CAAC;AAChB,IAAI,MAAM,OAAO,GAAG,MAAMA,iBAAU,CAAC,KAAK,CAAC,GAAG,KAAK,EAAE,GAAG,KAAK,CAAC,OAAO,EAAE,CAAC;AACxE,IAAI,MAAM,KAAK,GAAG,MAAM;AACxB,MAAM,IAAI,OAAO,EAAE;AACnB,QAAQ,YAAY,CAAC,OAAO,CAAC,CAAC;AAC9B,QAAQ,OAAO,GAAG,KAAK,CAAC,CAAC;AACzB,OAAO;AACP,MAAM,IAAI,UAAU,EAAE;AACtB,QAAQ,aAAa,CAAC,UAAU,CAAC,CAAC;AAClC,QAAQ,UAAU,GAAG,KAAK,CAAC,CAAC;AAC5B,OAAO;AACP,KAAK,CAAC;AACN,IAAI,EAAE,CAAC,gBAAgB,CAAC,WAAW,EAAE,CAAC,GAAG,KAAK;AAC9C,MAAM,IAAI,GAAG,CAAC,MAAM,KAAK,CAAC;AAC1B,QAAQ,OAAO;AACf,MAAM,KAAK,EAAE,CAAC;AACd,MAAM,OAAO,EAAE,CAAC;AAChB,MAAM,QAAQ,CAAC,gBAAgB,CAAC,SAAS,EAAE,MAAM,KAAK,EAAE,EAAE;AAC1D,QAAQ,IAAI,EAAE,IAAI;AAClB,OAAO,CAAC,CAAC;AACT,MAAM,OAAO,GAAG,UAAU,CAAC,MAAM;AACjC,QAAQ,UAAU,GAAG,WAAW,CAAC,MAAM;AACvC,UAAU,OAAO,EAAE,CAAC;AACpB,SAAS,EAAE,QAAQ,CAAC,CAAC;AACrB,OAAO,EAAE,KAAK,CAAC,CAAC;AAChB,KAAK,CAAC,CAAC;AACP,GAAG;AACH;;;;;;"}