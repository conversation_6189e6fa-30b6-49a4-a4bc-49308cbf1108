module.exports = {
  title: 'iohook',
  description: 'Node.js global native keyboard and mouse listener.',
  base: '/iohook/',
  themeConfig: {
    repo: 'wilix-team/iohook',
    editLinks: true,
    docsDir: 'docs',
    editLinkText: 'Edit this page on GitHub',
    sidebarDepth: 2,
    nav: [
      { text: 'Guide', link: '/' },
      { text: 'NPM', link: 'https://www.npmjs.com/package/iohook' },
    ],
    sidebar: [
      ['/', 'Home'],
      '/os-support',
      '/installation',
      '/usage',
      '/manual-build',
      '/faq',
    ],
  },
};
