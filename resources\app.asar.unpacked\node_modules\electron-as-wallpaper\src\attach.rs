use neon::prelude::*;

use windows::core::s;
use windows::Win32::{
    Foundation::{BOOL, HWND, LPARAM, WPARAM, RECT, POINT},
    Graphics::Gdi::{GetMonitorInfoA, MONITORINFO, MonitorFromWindow, MONITOR_DEFAULTTONEAREST},
    UI::WindowsAndMessaging,
};

use crate::input::start_input_forwarding;
use crate::window::{get_window_handle, toggle_window_transparent};

extern "system" fn enum_window(window: HWND, ref_worker_w: LPARAM) -> BOOL {
    unsafe {
        let shell_dll_def_view = WindowsAndMessaging::FindWindowExA(
            window,
            HWND::default(),
            s!("SHELLDLL_DefView"),
            None,
        )
        .unwrap_or(HWND::default());

        if !HWND::is_invalid(&shell_dll_def_view) {
            let worker_w =
                WindowsAndMessaging::FindWindowExA(HWND::default(), window, s!("WorkerW"), None)
                    .unwrap_or(HWND::default());

            if !HWND::is_invalid(&worker_w) {
                *(ref_worker_w.0 as *mut HWND) = worker_w;
            }
        }

        BOOL(1)
    }
}

#[derive(Default)]
struct AttachOptions {
    transparent: bool,
    forward_mouse_input: bool,
    forward_keyboard_input: bool,
}

impl AttachOptions {
    fn from_cx(cx: &mut FunctionContext) -> NeonResult<Self> {
        let mut options = Self::default();

        let obj = cx.argument::<JsObject>(1)?;

        options.transparent = obj.get::<JsBoolean, _, _>(cx, "transparent")?.value(cx);
        options.forward_mouse_input = obj
            .get::<JsBoolean, _, _>(cx, "forwardMouseInput")?
            .value(cx);
        options.forward_keyboard_input = obj
            .get::<JsBoolean, _, _>(cx, "forwardKeyboardInput")?
            .value(cx);

        Ok(options)
    }
}

pub fn attach(mut cx: FunctionContext) -> JsResult<JsUndefined> {
    let hwnd = get_window_handle(&mut cx)?;
    let options = AttachOptions::from_cx(&mut cx)?;

    unsafe {
        // Find WorkerW first
        let progman_hwnd = WindowsAndMessaging::FindWindowA(s!("Progman"), None).unwrap();
        WindowsAndMessaging::SendMessageTimeoutA(
            progman_hwnd,
            0x052C,
            WPARAM(0xD),
            LPARAM(0x1),
            WindowsAndMessaging::SMTO_NORMAL,
            1000,
            None,
        );
        let mut worker_w: HWND = HWND::default();
        WindowsAndMessaging::EnumWindows(
            Some(enum_window),
            LPARAM(&mut worker_w as *mut HWND as isize),
        ).unwrap();
        if HWND::is_invalid(&worker_w) {
            worker_w = WindowsAndMessaging::FindWindowExA(
                progman_hwnd,
                HWND::default(),
                s!("WorkerW"),
                None,
            ).unwrap();
        }

        // --- Get Target Monitor Info BEFORE SetParent ---
        let mut rect_before_parent = RECT::default();
        let _ = WindowsAndMessaging::GetWindowRect(hwnd, &mut rect_before_parent);
        // println!("[attach] Before SetParent: hwnd={:?}, GetWindowRect result: {:?}, rect: left={}, top={}, right={}, bottom={}", hwnd, get_window_rect_before_parent, rect_before_parent.left, rect_before_parent.top, rect_before_parent.right, rect_before_parent.bottom);

        let target_monitor = MonitorFromWindow(hwnd, MONITOR_DEFAULTTONEAREST);
        println!("[attach] Target Monitor handle (before SetParent): {:?}", target_monitor);
        let mut target_monitor_info = MONITORINFO { cbSize: std::mem::size_of::<MONITORINFO>() as u32, ..Default::default() };
        let get_monitor_info_result = GetMonitorInfoA(target_monitor, &mut target_monitor_info);
        println!("[attach] Target GetMonitorInfoA result (before SetParent): {:?}, monitor_info: {:?}", get_monitor_info_result, target_monitor_info);
        // Store the target monitor's rectangle and dimensions
        let target_rect = target_monitor_info.rcMonitor;
        let target_width = target_rect.right - target_rect.left;
        let target_height = target_rect.bottom - target_rect.top;
        println!("[attach] Target Monitor rect (before SetParent): left={}, top={}, right={}, bottom={}, width={}, height={}", target_rect.left, target_rect.top, target_rect.right, target_rect.bottom, target_width, target_height);
        // --- End Get Target Monitor Info ---


        // 设置父窗口
        println!("[attach] SetParent: hwnd={:?}, worker_w={:?}", hwnd, worker_w);
        WindowsAndMessaging::SetParent(hwnd, worker_w).unwrap();
        println!("[attach] SetParent called.");

        // // SetParent 后获取目标窗口所在的显示器 (REMOVED - We use the info from before SetParent)
        // let monitor = MonitorFromWindow(hwnd, MONITOR_DEFAULTTONEAREST);
        // println!("[attach] Monitor handle after SetParent: {:?}", monitor);
        // let mut monitor_info = MONITORINFO { cbSize: std::mem::size_of::<MONITORINFO>() as u32, ..Default::default() };
        // let get_monitor_info_result = GetMonitorInfoA(monitor, &mut monitor_info);
        // println!("[attach] GetMonitorInfoA result after SetParent: {:?}, monitor_info: {:?}", get_monitor_info_result, monitor_info);
        // let rect = monitor_info.rcMonitor;
        // let width = rect.right - rect.left;
        // let height = rect.bottom - rect.top;
        // println!("[attach] Monitor rect after SetParent: left={}, top={}, right={}, bottom={}, width={}, height={}", rect.left, rect.top, rect.right, rect.bottom, width, height);

        // 获取 WorkerW 窗口的屏幕坐标 (Needed AFTER SetParent)
        let mut worker_w_rect = RECT::default();
        let get_worker_w_rect_result = WindowsAndMessaging::GetWindowRect(worker_w, &mut worker_w_rect);
        println!("[attach] GetWindowRect for worker_w ({:?}) result: {:?}, rect: left={}, top={}, right={}, bottom={}", worker_w, get_worker_w_rect_result, worker_w_rect.left, worker_w_rect.top, worker_w_rect.right, worker_w_rect.bottom);

        // 计算目标显示器 (from before SetParent) 相对于 WorkerW 左上角的坐标
        let relative_left = target_rect.left - worker_w_rect.left;
        let relative_top = target_rect.top - worker_w_rect.top;
        println!("[attach] Calculated relative coordinates (using pre-SetParent monitor): relative_left={}, relative_top={}, calculation: {} - {} = {}",
            relative_left, relative_top, target_rect.top, worker_w_rect.top, target_rect.top - worker_w_rect.top);

        // 获取 WorkerW 的客户区坐标
        let mut worker_w_client_rect = RECT::default();
        let _ = WindowsAndMessaging::GetClientRect(worker_w, &mut worker_w_client_rect);
        println!("[attach] GetClientRect for worker_w: left={}, top={}, right={}, bottom={}",
            worker_w_client_rect.left, worker_w_client_rect.top, worker_w_client_rect.right, worker_w_client_rect.bottom);

        // 修改窗口样式，使其成为子窗口
        let style = WindowsAndMessaging::GetWindowLongPtrA(hwnd, WindowsAndMessaging::GWL_STYLE);
        let new_style = style | (WindowsAndMessaging::WS_CHILD.0 as isize);
        WindowsAndMessaging::SetWindowLongPtrA(hwnd, WindowsAndMessaging::GWL_STYLE, new_style);
        println!("[attach] Modified window style: old={:x}, new={:x}", style, new_style);

        // 计算客户区坐标 - 使用基于 pre-SetParent monitor 计算的相对坐标
        let client_x = relative_left;
        let client_y = relative_top;

        println!("[attach] Using calculated client coordinates: x={}, y={}, width={}, height={}",
            client_x, client_y, target_width, target_height); // Use target_width/height

        // 使用客户区坐标设置窗口位置
        WindowsAndMessaging::SetWindowPos(
            hwnd,
            HWND::default(),
            client_x,
            client_y,
            target_width,  // Use width from target monitor
            target_height, // Use height from target monitor
            WindowsAndMessaging::SWP_NOZORDER | WindowsAndMessaging::SWP_NOACTIVATE,
        ).unwrap();
        println!("[attach] SetWindowPos called with calculated client coordinates.");

        // 设置后获取窗口位置 (用于验证)
        let mut actual_rect_final = RECT::default();
        let _ = WindowsAndMessaging::GetWindowRect(hwnd, &mut actual_rect_final);
        println!("[attach] After SetWindowPos: hwnd={:?}, actual window rect: left={}, top={}, right={}, bottom={}",
            hwnd, actual_rect_final.left, actual_rect_final.top, actual_rect_final.right, actual_rect_final.bottom);

        if options.transparent {
            toggle_window_transparent(hwnd, true);
        }
        start_input_forwarding(
            hwnd,
            options.forward_mouse_input,
            options.forward_keyboard_input,
        );
        Ok(cx.undefined())
    }
}
