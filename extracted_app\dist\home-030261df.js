import{d as ge,u as pe,r as i,a as N,o as n,c,b as F,w as oe,e as u,t as v,f as _,O as Ge,_ as _e,F as O,g as Q,h as J,i as g,j as C,k as Pe,l as p,m as Je,E as z,n as Ke,p as je,q as He,s as Ye,v as Ie,B as ve,x as de,y as Se,z as Xe,A as Ze,D as et,C as me,G as te,H as be,I as tt,J as ae,P as at,T as ot}from"./index-7f6647f0.js";import{_ as lt}from"./network-a35c6432.js";import{d as st}from"./downloadList-31c1c968.js";import{u as nt}from"./updatePrompt-ca30b371.js";const ut="TimeLimited-5083b4bf.png",it="TimeLimited_en-1fe85d04.png",ct={class:"QRCode_wrap"},rt=["src"],vt={class:"way_wrap"},dt={class:"pour"},mt=ge({__name:"butPopup",emits:["paymentSuccessful"],setup(he,{expose:U,emit:R}){const{t:a}=pe(),y=R,s=i(!1);i(1);const S=i({}),L=b=>{S.value=b,s.value=!0},I=()=>{Ge({out_trade_no:S.value.out_trade_no}).then(b=>{b.code==200&&(b.data.status==1&&y("paymentSuccessful"),s.value=!1)})};return U({onShowEvent:L}),(b,P)=>{const q=N("el-dialog");return n(),c("div",null,[F(q,{modelValue:s.value,"onUpdate:modelValue":P[0]||(P[0]=r=>s.value=r),width:"320","close-delay":0,top:"31vh",style:{"border-radius":"14px"},"close-on-click-modal":!1},{default:oe(()=>[u("div",ct,[u("div",{class:"guanbi iconfont icon-guanbi",onClick:I}),u("img",{src:S.value.QR_code_base64,class:"QRCode",alt:""},null,8,rt),u("div",vt,[P[1]||(P[1]=u("div",{class:"icon iconfont icon-weixinzhifu"},null,-1)),u("span",null,v(_(a)("version.text1")),1)]),u("div",dt,v(_(a)("version.text2")),1)])]),_:1},8,["modelValue"])])}}});const xe=_e(mt,[["__scopeId","data-v-c01bb225"]]),gt={class:"dialog_wrap"},pt={class:"Carousel"},_t=["src"],ht={class:"info"},ft={class:"name"},yt={class:"description"},Tt={class:"money"},kt=ge({__name:"typingDetails",setup(he,{expose:U}){const{t:R}=pe(),a=i(!1),y=i(""),s=i({}),S=i(""),L=f=>{s.value=f,y.value=localStorage.getItem("curPetId"),a.value=!0},I=()=>{a.value=!1},b=()=>{V()||Pe({payId:1,goodsId:s.value.goodsId}).then(f=>{if(f.code==200){S.value.onShowEvent(f.data);return}})},P=()=>{V()||(p.send("useTyping",s.value.id),I())},q=()=>{I(),p.send("closePet22")},r=()=>{I(),p.send("waySuccess")},V=()=>{let f=!1;return localStorage.getItem("token")||localStorage.setItem("token","fake_token_logged_in"),f};return U({onShowEvent:L,onclose:I}),(f,T)=>{const W=N("el-carousel-item"),K=N("el-carousel"),d=N("el-dialog");return n(),c("div",null,[F(d,{modelValue:a.value,"onUpdate:modelValue":T[3]||(T[3]=o=>a.value=o),width:"1043","close-delay":0,top:"22vh",style:{"border-radius":"14px"},onClose:I,"close-on-click-modal":!1},{default:oe(()=>[F(xe,{ref_key:"butPopupRef",ref:S,onPaymentSuccessful:r},null,512),u("div",gt,[u("div",{class:"icon iconfont icon-guanbi",onClick:I}),u("div",pt,[F(K,{"indicator-position":"outside",autoplay:""},{default:oe(()=>[(n(!0),c(O,null,Q(s.value.carouselImage,o=>(n(),J(W,{key:o},{default:oe(()=>[u("img",{src:o,style:{width:"100%",height:"100%"},alt:""},null,8,_t)]),_:2},1024))),128))]),_:1})]),u("div",ht,[u("div",ft,v(s.value.title),1),u("div",yt,v(s.value.description),1),u("div",Tt,v(s.value.price),1),s.value.showBay==1&&s.value.isTime!=2?(n(),c("div",{key:0,class:"but",onClick:b},v(_(R)("home.but")),1)):g("",!0),s.value.showBay!=1&&s.value.id!=y.value&&s.value.isTime!=2?(n(),c("div",{key:1,class:"but",onClick:T[0]||(T[0]=C(o=>P(s.value.id),["stop"]))},v(_(R)("home.but1")),1)):g("",!0),s.value.showBay!=1&&s.value.id!=y.value&&s.value.isTime==2?(n(),c("div",{key:2,class:"but",onClick:T[1]||(T[1]=C(o=>P(s.value.id),["stop"]))},v(_(R)("home.but1")),1)):g("",!0),s.value.id==y.value?(n(),c("div",{key:3,class:"but",onClick:T[2]||(T[2]=C(o=>q(),["stop"]))},v(_(R)("home.but2")),1)):g("",!0)])])]),_:1},8,["modelValue"])])}}});const wt=_e(kt,[["__scopeId","data-v-9cd98963"]]),It={class:"app"},St={class:"category"},bt=["onClick"],Pt={class:"span"},xt={key:0,class:"nav"},Ct={class:"menu"},Rt=["onClick"],Lt={class:"span"},$t={class:"Filter"},Dt={key:0,class:"dropdown",style:{right:"-50px"}},Et=["onClick"],Bt={class:"Filter"},Mt={key:0,class:"dropdown"},Nt=["onClick"],Ft={key:1,class:"nav",style:{"justify-content":"flex-start"}},Vt={class:"search_text"},zt={key:2,class:"content"},Ut={class:"network_no"},qt={class:"text"},Wt={key:3,class:"content"},At={class:"wrap"},Ot={class:"left"},Qt=["onClick"],Gt={class:"image_title"},Jt={class:"text"},Kt={class:"image_wrap"},jt=["src"],Ht={key:0,src:ut,class:"isTime",alt:""},Yt={key:1,src:it,class:"isTime",alt:""},Xt={key:2,class:"info_wrap"},Zt={class:"info"},ea={class:"name"},ta={class:"price"},aa=["onClick"],oa=["onClick"],la=["onClick"],sa={key:3,class:"progress"},na={key:1},ua={class:"right"},ia={key:0,class:"paging"},ca=ge({__name:"home",setup(he){const{shell:U}=require("electron"),R=require("fs"),{t:a}=pe(),y=Je(),s=st(),S=i(""),L=i(""),I=i(""),b=i(""),P=i(""),q=i(""),r=i({tags:[]}),V=i(""),f=i(!0),T=i(!1),W=i(0),K=i(0),d=i({currentPage:1,selectedId:1,accountId:"",requiredTags:"",fileType:"",searchText:"",imageQuality:"全部"}),o=i([]),B=i(0),x=i(localStorage.getItem("curPetId")),j=i(!1),H=i(!1),le=i(a("home.menu12")),se=i(a("home.menu1")),A=i(0),h=i(0),$=i([]),ne=i(""),Y=i(navigator.onLine),Ce=i([{name:a("home.nav1"),tagType:""},{name:a("home.nav2"),tagType:"Anime"},{name:a("home.nav3"),tagType:"Game"},{name:a("home.nav4"),tagType:"Cartoon"},{name:a("home.nav5"),tagType:"sCENERY"},{name:a("home.nav6"),tagType:"tECH"},{name:a("home.nav7"),tagType:"aNIMAL"},{name:a("home.nav8"),tagType:"MMD"},{name:a("home.nav9"),tagType:"Pixel"},{name:a("home.nav10"),tagType:"Film"},{name:a("home.nav11"),tagType:"Cute"},{name:a("home.nav12"),tagType:"General"}]),Re=i([{name:a("home.nav1"),tagType:""},{name:a("home.menu4"),tagType:"video"},{name:a("home.menu5"),tagType:"hudong"},{name:a("home.menu9"),tagType:"clock"},{name:a("home.menu10"),tagType:"typing"},{name:a("home.menu11"),tagType:"music"},{name:a("home.menu6"),tagType:"mouse"}]),Le=i([{name:a("home.menu1"),RuleType:1},{name:a("home.menu7"),RuleType:2},{name:a("home.menu8"),RuleType:3}]),$e=i([{name:a("home.menu12"),RuleType:"全部"},{name:a("home.menu13"),RuleType:"4K"},{name:a("home.menu14"),RuleType:"2K"},{name:a("home.menu15"),RuleType:"1080P"},{name:a("home.menu16"),RuleType:"带鱼屏"}]),De=t=>{se.value=t.name,d.value.selectedId=t.RuleType,j.value=!1,k()},Ee=t=>{le.value=t.name,d.value.imageQuality=t.RuleType,H.value=!1,k()},Be=()=>{f.value=!0,y.searchText="",d.value.currentPage=1,d.value.accountId="",d.value.selectedId=1,d.value.fileType="",d.value.searchText="",d.value.requiredTags="",A.value=0,k()},fe=()=>ne.value.includes("zh"),ye=(t,e)=>{if(localStorage.removeItem("currentPage"),localStorage.removeItem("tagType"),localStorage.setItem("category",t.tagType),localStorage.setItem("curMenuIndex","0"),localStorage.setItem("curCatIndex",e),h.value=e,A.value=0,d.value.currentPage=1,d.value.requiredTags="",t.tagType?d.value.fileType=t.tagType:d.value.fileType="",d.value.fileType=="typing"){ot().then(m=>{if(m.code==200&&m.data.status!=0){P.value.onShowEvent(m.data.updateUrl);return}k()});return}k()},Me=()=>{ye({tagType:""},0)},Ne=(t,e)=>{localStorage.removeItem("currentPage"),localStorage.setItem("tagType",t.tagType),localStorage.setItem("curMenuIndex",e),A.value=e,d.value.currentPage=1,t.tagType?d.value.requiredTags=t.tagType:d.value.requiredTags="",k()},X=()=>{localStorage.removeItem("curPetId"),x.value=localStorage.getItem("curPetId"),localStorage.setItem("closePet","false"),p.send("closePet",!1)},Te=async()=>{let t=!1;const e=await de({goodsId:r.value.goodsId});return(e==null?void 0:e.code)!==200&&(p.send("againLogin"),t=!0),t},G=async t=>{await Te()||ve({id:t}).then(e=>{e.code==200&&(r.value=e.data,de({goodsId:e.data.goodsId}).then(m=>{L.value&&L.value.onShowEvent(r.value),Se().then(async E=>{if(!E.data.path){Z();return}const ce=E.data.path.lastIndexOf("\\"),re=E.data.path.substring(0,ce);R.access(re,R.constants.F_OK,l=>{if(l){console.log("找不到",l),Z();return}localStorage.setItem("closePet","true"),localStorage.setItem("curPetId",r.value.id),x.value=localStorage.getItem("curPetId"),E.code==200&&p.send("restartTyping",E.data)})})}))})},Fe=(t,e)=>{r.value=t,ee(t,!0)},ke=async t=>{await Te()||ve({id:t.id}).then(e=>{e.code==200&&(r.value=e.data,L.value.onShowEvent(r.value),Pe({payId:1,goodsId:e.data.goodsId}).then(m=>{m.code==200&&b.value.onShowEvent(m.data)}))})},Z=()=>{ee(r.value);let t=-1;$.value.forEach((e,m)=>{e.id===r.value.id&&(t=m)}),$.value[t].showBay=2,!o.value.some(e=>e.id===r.value.id)&&(o.value.push(r.value),s.setList(o.value),!(o.value.length>1)&&D())},we=t=>{V.value=t,f.value=!1,d.value.accountId=t,d.value.currentPage=1,A.value=0,Qe()},Ve=t=>o.value.some(e=>e.id===t);let ue=null;const ze=()=>{if(ue){z.error(a("home.text2"));return}ue=setTimeout(()=>{ue=null},1e3),Xe(h.value,r.value).then(t=>{if(t){if(o.value.some(e=>e.id===r.value.id))return;if(s.getLists().length!==0){z.error(a("System.text1"));return}Ze(r.value).then(e=>{if(e){z.error(a("home.text1"));return}et({id:r.value.id}).then(m=>{if(m.code==200){if(r.value.path=m.data.path,o.value.push(r.value),s.setList(o.value),o.value.length>1)return;D();return}z.error(m.message)})})}else{let e=6,m="rainmeter";h.value==6&&(e=8,m="mouse"),q.value.onShowEvent(e,m)}})},ie=i(""),D=()=>{const t=JSON.stringify({url:o.value[0].path,gifUrl:o.value[0].previewUrl,title:o.value[0].title,id:o.value[0].id,accountId:o.value[0].accountId,description:o.value[0].description,resolution:o.value[0].resolution,size:o.value[0].size,tags:o.value[0].tags,userName:o.value[0].userName,fileType:o.value[0].fileType,local:!1,musicFileName:o.value[0].musicFileName});ie.value=setTimeout(()=>{B.value=0,s.delList(),o.value=s.list,o.value.length>0&&D()},5e3),o.value[0].fileType=="video"?p.invoke("DownloadInfo",t).then(e=>{me(y,e),te({id:o.value[0].id}),B.value=0,s.delList(),o.value=s.list,o.value.length>0&&D()}).catch(e=>{z.error(a("home.text1")),s.delList(),o.value=s.list,o.value.length>0&&D()}):o.value[0].fileType=="hudong"||o.value[0].fileType=="clock"?p.invoke("DownloadASARlocal",t).then(e=>{me(y,e),te({id:e.id}).then(m=>{B.value=0,s.delList(),o.value=s.list,o.value.length>0&&D()})}):o.value[0].fileType=="typing"?p.invoke("DownloadTyping",t).then(e=>{de({goodsId:r.value.goodsId,path:e}).then(m=>{localStorage.setItem("closePet","true"),localStorage.setItem("curPetId",r.value.id),x.value=localStorage.getItem("curPetId"),Se().then(E=>{p.send("restartTyping",E.data)}),o.value.length!=0&&(te({id:o.value[0].id}),B.value=0,s.delList(),o.value=s.list,o.value.length>0&&D())})}):(o.value[0].fileType=="music"||o.value[0].fileType=="mouse")&&p.invoke("DownloadMusicScore",t).then(e=>{me(y,e),te({id:e.id}).then(m=>{B.value=0,s.delList(),o.value=s.list,o.value.length>0&&D()})})},Ue=t=>{d.value.currentPage=t,localStorage.setItem("currentPage",t),k()},qe=i(""),We=t=>{U.openExternal(t.url)},Ae=i(""),Oe=t=>{U.openExternal(t.url)},k=()=>{Y.value&&(T.value=!1,$.value=[],be(d.value).then(t=>{W.value=t.data.totalCount,K.value=t.data.pageSize,$.value=t.data.currentPageRecords,T.value=!0,tt(()=>{if(h.value==4)return;const e=qe.value;if(!e)return;e.addEventListener("new-window",We),Ae.value.addEventListener("new-window",Oe)}),ee($.value[0])}))},ee=(t,e)=>{t&&ve({id:t.id}).then(m=>{if(m){if(r.value=m.data,e&&h.value==4&&I.value.onShowEvent(m.data),h.value==4){L.value.onShowEvent(r.value);return}S.value&&S.value.onShowEvent(r.value)}})},Qe=()=>{localStorage.removeItem("currentPage"),T.value=!1,$.value=[],be(d.value).then(t=>{W.value=t.data.totalCount,$.value=t.data.currentPageRecords,T.value=!0,ee($.value[0])})};return p.on("loginSuccessEvent",(t,e)=>{k()}),p.on("useTyping",(t,e)=>{G(e)}),p.on("onClosePet",(t,e)=>{X()}),p.on("UploadSuccessful",(t,e)=>{k()}),p.on("onWaySuccess",(t,e)=>{Z()}),p.on("DownloadFailure",(t,e)=>{z.error(a("home.text1")),s.delList(),o.value=s.list,o.value.length>0&&D()}),p.on("DownloadProgress",(t,e)=>{clearTimeout(ie.value),ie.value=null,B.value=e.toFixed(2)}),p.on("RefreshList",(t,e)=>{k()}),p.on("JudgingTypingFollow",(t,e)=>{Ke({uniqueId:e}).then(m=>{m.code!=200&&(z.error(m.message),X())})}),je(()=>y.searchText,(t,e)=>{localStorage.removeItem("currentPage"),d.value.currentPage=1,d.value.searchText=t,t?f.value=!1:f.value=!0,V.value=d.value.searchText,k()}),He(()=>{B.value=s.progress,o.value=s.list}),Ye(()=>{localStorage.getItem("setLang")?ne.value=localStorage.getItem("setLang"):ne.value=p.sendSync("Language"),d.value.currentPage=localStorage.getItem("currentPage")||1,d.value.requiredTags=localStorage.getItem("tagType"),d.value.fileType=localStorage.getItem("category"),A.value=Number(localStorage.getItem("curMenuIndex"))||0,h.value=Number(localStorage.getItem("curCatIndex"))||0,y.searchText?(d.value.searchText=y.searchText,f.value=!1,V.value=d.value.searchText,k()):(k(),o.value=s.list)}),(t,e)=>{const m=N("el-progress"),E=N("WallpaperDetails"),ce=N("typingInfo"),re=N("paging");return n(),c("div",It,[u("div",St,[(n(!0),c(O,null,Q(Re.value,(l,w)=>(n(),c("div",{class:ae(["category_item",h.value==w?"cta_active":""]),key:w,onClick:M=>ye(l,w)},[u("div",Pt,v(l.name),1),e[8]||(e[8]=u("div",{class:"category_bg"},null,-1))],10,bt))),128))]),f.value?(n(),c("div",xt,[u("div",Ct,[(n(!0),c(O,null,Q(Ce.value,(l,w)=>(n(),c("div",{class:ae(["ment_item",A.value==w?"active":""]),key:w,onClick:M=>Ne(l,w)},[u("div",Lt,v(l.name),1)],10,Rt))),128))]),u("div",$t,[u("div",{class:"show",onClick:e[0]||(e[0]=l=>H.value=!H.value)},[Ie(v(le.value)+" ",1),e[9]||(e[9]=u("div",{class:"iconfont icon-xiala icon"},null,-1))]),H.value?(n(),c("div",Dt,[(n(!0),c(O,null,Q($e.value,(l,w)=>(n(),c("div",{class:ae(["dropdown-item",le.value==l.name?"dropdown-item-avtive":""]),key:w,onClick:M=>Ee(l)},v(l.name),11,Et))),128))])):g("",!0)]),u("div",Bt,[u("div",{class:"show",onClick:e[1]||(e[1]=l=>j.value=!j.value)},[Ie(v(se.value)+" ",1),e[10]||(e[10]=u("div",{class:"iconfont icon-xiala icon"},null,-1))]),j.value?(n(),c("div",Mt,[(n(!0),c(O,null,Q(Le.value,(l,w)=>(n(),c("div",{class:ae(["dropdown-item",se.value==l.name?"dropdown-item-avtive":""]),key:w,onClick:M=>De(l)},v(l.name),11,Nt))),128))])):g("",!0)])])):g("",!0),f.value?g("",!0):(n(),c("div",Ft,[u("div",{class:"searchIcon iconfont icon-fanhui",onClick:Be}),u("div",Vt,v(_(a)("home.Search"))+" "+v(V.value)+" "+v(_(a)("home.Search1"))+" "+v(W.value)+" "+v(_(a)("home.Search2")),1)])),Y.value?g("",!0):(n(),c("div",zt,[u("div",Ut,[e[11]||(e[11]=u("img",{src:lt,class:"network",alt:""},null,-1)),u("div",qt,[u("p",null,v(_(a)("home.prompt1")),1),u("p",null,v(_(a)("home.prompt2")),1)]),u("div",{class:"networkIcon iconfont icon-shuaxin",onClick:e[2]||(e[2]=l=>k())})])])),Y.value?(n(),c("div",Wt,[u("div",At,[u("div",Ot,[(n(!0),c(O,null,Q($.value,(l,w)=>(n(),c("div",{class:"item",key:w,onClick:M=>Fe(l)},[u("div",Gt,[u("div",Jt,v(l.title),1)]),u("div",Kt,[u("img",{src:l.previewUrl,class:"image",alt:""},null,8,jt)]),l.isTime==2&&fe()?(n(),c("img",Ht)):g("",!0),l.isTime==2&&!fe()?(n(),c("img",Yt)):g("",!0),h.value==4?(n(),c("div",Xt,[u("div",Zt,[u("div",ea,v(l.title),1),u("div",ta,v(l.price),1)]),l.showBay==1&&l.isTime!=2?(n(),c("div",{key:0,class:"buy",onClick:C(M=>ke(l),["stop"])},v(_(a)("home.but")),9,aa)):g("",!0),h.value==4&&l.showBay!=1&&l.id!=x.value&&l.isTime!=2?(n(),c("div",{key:1,class:"buy",onClick:C(M=>G(l.id,l.isTime),["stop"])},v(_(a)("home.but1")),9,oa)):g("",!0),h.value==4&&l.id!=x.value&&l.isTime==2&&l.id!=x.value?(n(),c("div",{key:2,class:"buy",onClick:C(M=>G(l.id),["stop"])},v(_(a)("home.but1")),9,la)):g("",!0),h.value==4&&l.id==x.value?(n(),c("div",{key:3,class:"buy",onClick:e[3]||(e[3]=C(M=>X(),["stop"]))},v(_(a)("home.but2")),1)):g("",!0)])):g("",!0),Ve(l.id)?(n(),c("div",sa,[o.value[0].id==l.id?(n(),J(m,{key:0,type:"dashboard",percentage:parseFloat(B.value)},null,8,["percentage"])):(n(),c("div",na,v(_(a)("home.dowText")),1))])):g("",!0)],8,Qt))),128))]),u("div",ua,[h.value!=4?(n(),J(E,{key:0,ref_key:"WallpaperDetailsRef",ref:S,onAccountId:we},null,512)):g("",!0),h.value==4?(n(),J(ce,{key:1,ref_key:"typingInfoRef",ref:L,onAccountId:we},null,512)):g("",!0),h.value!=4?(n(),c("div",{key:2,class:"but",onClick:ze},v(_(a)("General.text6")),1)):g("",!0),h.value==4&&r.value.showBay==1&&r.value.isTime!=2?(n(),c("div",{key:3,class:"but",onClick:e[4]||(e[4]=l=>ke(r.value))},v(_(a)("home.but")),1)):g("",!0),h.value==4&&r.value.showBay!=1&&r.value.id!=x.value&&r.value.isTime!=2?(n(),c("div",{key:4,class:"but",onClick:e[5]||(e[5]=C(l=>G(r.value.id),["stop"]))},v(_(a)("home.but1")),1)):g("",!0),h.value==4&&r.value.isTime==2&&r.value.id!=x.value?(n(),c("div",{key:5,class:"but",onClick:e[6]||(e[6]=C(l=>G(r.value.id),["stop"]))},v(_(a)("home.but1")),1)):g("",!0),h.value==4&&r.value.id==x.value?(n(),c("div",{key:6,class:"but",onClick:e[7]||(e[7]=C(l=>X(),["stop"]))},v(_(a)("home.but2")),1)):g("",!0)])]),Y.value?(n(),c("div",ia,[T.value?(n(),J(re,{key:0,onOnChangePage:Ue,pageSize:K.value,total:W.value,pagingRight:!1,currentPage:d.value.currentPage},null,8,["pageSize","total","currentPage"])):g("",!0)])):g("",!0)])):g("",!0),F(wt,{ref_key:"typingDetailsRef",ref:I},null,512),F(xe,{ref_key:"butPopupRef",ref:b,onPaymentSuccessful:Z},null,512),F(nt,{ref_key:"updatePromptRef",ref:P,onClose:Me},null,512),F(at,{ref_key:"PMTRef",ref:q},null,512)])}}});const ga=_e(ca,[["__scopeId","data-v-b1789eb0"]]);export{ga as default};
