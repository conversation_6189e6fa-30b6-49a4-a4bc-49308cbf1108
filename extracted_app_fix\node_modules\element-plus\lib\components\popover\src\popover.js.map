{"version": 3, "file": "popover.js", "sources": ["../../../../../../packages/components/popover/src/popover.ts"], "sourcesContent": ["import { buildProps, isBoolean } from '@element-plus/utils'\nimport {\n  useTooltipContentProps,\n  useTooltipTriggerProps,\n} from '@element-plus/components/tooltip'\nimport { dropdownProps } from '@element-plus/components/dropdown'\nimport type { ExtractPropTypes, PropType } from 'vue'\nimport type Popover from './popover.vue'\n\nexport const popoverProps = buildProps({\n  /**\n   * @description how the popover is triggered\n   */\n  trigger: useTooltipTriggerProps.trigger,\n  /**\n   * @description popover placement\n   */\n  placement: dropdownProps.placement,\n  /**\n   * @description whether Popover is disabled\n   */\n  disabled: useTooltipTriggerProps.disabled,\n  /**\n   * @description whether popover is visible\n   */\n  visible: useTooltipContentProps.visible,\n  /**\n   * @description popover transition animation\n   */\n  transition: useTooltipContentProps.transition,\n  /**\n   * @description parameters for [popper.js](https://popper.js.org/docs/v2/)\n   */\n  popperOptions: dropdownProps.popperOptions,\n  /**\n   * @description [tabindex](https://developer.mozilla.org/en-US/docs/Web/HTML/Global_attributes/tabindex) of Popover\n   */\n  tabindex: dropdownProps.tabindex,\n  /**\n   * @description popover content, can be replaced with a default `slot`\n   */\n  content: useTooltipContentProps.content,\n  /**\n   * @description custom style for popover\n   */\n  popperStyle: useTooltipContentProps.popperStyle,\n  /**\n   * @description custom class name for popover\n   */\n  popperClass: useTooltipContentProps.popperClass,\n  enterable: {\n    ...useTooltipContentProps.enterable,\n    default: true,\n  },\n  /**\n   * @description Tooltip theme, built-in theme: `dark` / `light`\n   */\n  effect: {\n    ...useTooltipContentProps.effect,\n    default: 'light',\n  },\n  /**\n   * @description whether popover dropdown is teleported to the body\n   */\n  teleported: useTooltipContentProps.teleported,\n  /**\n   * @description popover title\n   */\n  title: String,\n  /**\n   * @description popover width\n   */\n  width: {\n    type: [String, Number],\n    default: 150,\n  },\n  /**\n   * @description popover offset\n   */\n  offset: {\n    type: Number,\n    default: undefined,\n  },\n  /**\n   * @description delay of appearance, in millisecond\n   */\n  showAfter: {\n    type: Number,\n    default: 0,\n  },\n  /**\n   * @description delay of disappear, in millisecond\n   */\n  hideAfter: {\n    type: Number,\n    default: 200,\n  },\n  /**\n   * @description timeout in milliseconds to hide tooltip\n   */\n  autoClose: {\n    type: Number,\n    default: 0,\n  },\n  /**\n   * @description whether a tooltip arrow is displayed or not. For more info, please refer to [ElPopper](https://github.com/element-plus/element-plus/tree/dev/packages/components/popper)\n   */\n  showArrow: {\n    type: Boolean,\n    default: true,\n  },\n  /**\n   * @description when popover inactive and `persistent` is `false` , popover will be destroyed\n   */\n  persistent: {\n    type: Boolean,\n    default: true,\n  },\n  'onUpdate:visible': {\n    type: Function as PropType<(visible: boolean) => void>,\n  },\n} as const)\nexport type PopoverProps = ExtractPropTypes<typeof popoverProps>\n\nexport const popoverEmits = {\n  'update:visible': (value: boolean) => isBoolean(value),\n  'before-enter': () => true,\n  'before-leave': () => true,\n  'after-enter': () => true,\n  'after-leave': () => true,\n}\nexport type PopoverEmits = typeof popoverEmits\n\nexport type PopoverInstance = InstanceType<typeof Popover> & unknown\n"], "names": ["buildProps", "useTooltipTriggerProps", "dropdownProps", "useTooltipContentProps", "isBoolean"], "mappings": ";;;;;;;;;;AAMY,MAAC,YAAY,GAAGA,kBAAU,CAAC;AACvC,EAAE,OAAO,EAAEC,8BAAsB,CAAC,OAAO;AACzC,EAAE,SAAS,EAAEC,sBAAa,CAAC,SAAS;AACpC,EAAE,QAAQ,EAAED,8BAAsB,CAAC,QAAQ;AAC3C,EAAE,OAAO,EAAEE,8BAAsB,CAAC,OAAO;AACzC,EAAE,UAAU,EAAEA,8BAAsB,CAAC,UAAU;AAC/C,EAAE,aAAa,EAAED,sBAAa,CAAC,aAAa;AAC5C,EAAE,QAAQ,EAAEA,sBAAa,CAAC,QAAQ;AAClC,EAAE,OAAO,EAAEC,8BAAsB,CAAC,OAAO;AACzC,EAAE,WAAW,EAAEA,8BAAsB,CAAC,WAAW;AACjD,EAAE,WAAW,EAAEA,8BAAsB,CAAC,WAAW;AACjD,EAAE,SAAS,EAAE;AACb,IAAI,GAAGA,8BAAsB,CAAC,SAAS;AACvC,IAAI,OAAO,EAAE,IAAI;AACjB,GAAG;AACH,EAAE,MAAM,EAAE;AACV,IAAI,GAAGA,8BAAsB,CAAC,MAAM;AACpC,IAAI,OAAO,EAAE,OAAO;AACpB,GAAG;AACH,EAAE,UAAU,EAAEA,8BAAsB,CAAC,UAAU;AAC/C,EAAE,KAAK,EAAE,MAAM;AACf,EAAE,KAAK,EAAE;AACT,IAAI,IAAI,EAAE,CAAC,MAAM,EAAE,MAAM,CAAC;AAC1B,IAAI,OAAO,EAAE,GAAG;AAChB,GAAG;AACH,EAAE,MAAM,EAAE;AACV,IAAI,IAAI,EAAE,MAAM;AAChB,IAAI,OAAO,EAAE,KAAK,CAAC;AACnB,GAAG;AACH,EAAE,SAAS,EAAE;AACb,IAAI,IAAI,EAAE,MAAM;AAChB,IAAI,OAAO,EAAE,CAAC;AACd,GAAG;AACH,EAAE,SAAS,EAAE;AACb,IAAI,IAAI,EAAE,MAAM;AAChB,IAAI,OAAO,EAAE,GAAG;AAChB,GAAG;AACH,EAAE,SAAS,EAAE;AACb,IAAI,IAAI,EAAE,MAAM;AAChB,IAAI,OAAO,EAAE,CAAC;AACd,GAAG;AACH,EAAE,SAAS,EAAE;AACb,IAAI,IAAI,EAAE,OAAO;AACjB,IAAI,OAAO,EAAE,IAAI;AACjB,GAAG;AACH,EAAE,UAAU,EAAE;AACd,IAAI,IAAI,EAAE,OAAO;AACjB,IAAI,OAAO,EAAE,IAAI;AACjB,GAAG;AACH,EAAE,kBAAkB,EAAE;AACtB,IAAI,IAAI,EAAE,QAAQ;AAClB,GAAG;AACH,CAAC,EAAE;AACS,MAAC,YAAY,GAAG;AAC5B,EAAE,gBAAgB,EAAE,CAAC,KAAK,KAAKC,eAAS,CAAC,KAAK,CAAC;AAC/C,EAAE,cAAc,EAAE,MAAM,IAAI;AAC5B,EAAE,cAAc,EAAE,MAAM,IAAI;AAC5B,EAAE,aAAa,EAAE,MAAM,IAAI;AAC3B,EAAE,aAAa,EAAE,MAAM,IAAI;AAC3B;;;;;"}