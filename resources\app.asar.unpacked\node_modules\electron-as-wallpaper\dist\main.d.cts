import { BrowserWindow } from 'electron';

declare module "electron" {
    interface BrowserWindow {
        wallpaperState?: {
            isAttached?: boolean;
            isTransparent?: boolean;
            isForwardMouseInput?: boolean;
            isForwardKeyboardInput?: boolean;
        };
    }
}
interface AttachOptions {
    /**
     * Makes the window transparent.
     * platform: Windows
     * @default false
     */
    transparent?: boolean;
    /**
     * Forward mouse input-forwarding to the window.
     * platform: Windows
     * @default false
     */
    forwardMouseInput?: boolean;
    /**
     * Forward keyboard input-forwarding to the window.
     * platform: Windows
     * @default false
     */
    forwardKeyboardInput?: boolean;
}
declare const attach: (window: BrowserWindow, options?: AttachOptions) => void;
declare const detach: (window: BrowserWindow) => void;
declare const reset: () => void;

export { attach, detach, reset };
