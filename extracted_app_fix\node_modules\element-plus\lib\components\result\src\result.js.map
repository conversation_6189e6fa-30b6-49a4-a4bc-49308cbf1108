{"version": 3, "file": "result.js", "sources": ["../../../../../../packages/components/result/src/result.vue"], "sourcesContent": ["<template>\n  <div :class=\"ns.b()\">\n    <div :class=\"ns.e('icon')\">\n      <slot name=\"icon\">\n        <component\n          :is=\"resultIcon.component\"\n          v-if=\"resultIcon.component\"\n          :class=\"resultIcon.class\"\n        />\n      </slot>\n    </div>\n    <div v-if=\"title || $slots.title\" :class=\"ns.e('title')\">\n      <slot name=\"title\">\n        <p>{{ title }}</p>\n      </slot>\n    </div>\n    <div v-if=\"subTitle || $slots['sub-title']\" :class=\"ns.e('subtitle')\">\n      <slot name=\"sub-title\">\n        <p>{{ subTitle }}</p>\n      </slot>\n    </div>\n    <div v-if=\"$slots.extra\" :class=\"ns.e('extra')\">\n      <slot name=\"extra\" />\n    </div>\n  </div>\n</template>\n\n<script lang=\"ts\" setup>\nimport { computed } from 'vue'\nimport { useNamespace } from '@element-plus/hooks'\nimport { IconComponentMap, IconMap, resultProps } from './result'\n\ndefineOptions({\n  name: 'ElResult',\n})\n\nconst props = defineProps(resultProps)\n\nconst ns = useNamespace('result')\n\nconst resultIcon = computed(() => {\n  const icon = props.icon\n  const iconClass = icon && IconMap[icon] ? IconMap[icon] : 'icon-info'\n  const iconComponent =\n    IconComponentMap[iconClass] || IconComponentMap['icon-info']\n\n  return {\n    class: iconClass,\n    component: iconComponent,\n  }\n})\n</script>\n"], "names": ["useNamespace", "computed", "IconMap", "IconComponentMap"], "mappings": ";;;;;;;;;uCAgCc,CAAA;AAAA,EACZ,IAAM,EAAA,UAAA;AACR,CAAA,CAAA,CAAA;;;;;;AAIA,IAAM,MAAA,EAAA,GAAKA,mBAAa,QAAQ,CAAA,CAAA;AAEhC,IAAM,MAAA,UAAA,GAAaC,aAAS,MAAM;AAChC,MAAA,MAAM,OAAO,KAAM,CAAA,IAAA,CAAA;AACnB,MAAA,MAAM,YAAY,IAAQ,IAAAC,cAAA,CAAQ,IAAI,CAAI,GAAAA,cAAA,CAAQ,IAAI,CAAI,GAAA,WAAA,CAAA;AAC1D,MAAA,MAAM,aACJ,GAAAC,uBAAA,CAAiB,SAAS,CAAA,IAAKA,wBAAiB,WAAW,CAAA,CAAA;AAE7D,MAAO,OAAA;AAAA,QACL,KAAO,EAAA,SAAA;AAAA,QACP,SAAW,EAAA,aAAA;AAAA,OACb,CAAA;AAAA,KACD,CAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;"}