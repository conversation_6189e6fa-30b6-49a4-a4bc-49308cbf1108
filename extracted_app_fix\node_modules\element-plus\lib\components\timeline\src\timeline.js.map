{"version": 3, "file": "timeline.js", "sources": ["../../../../../../packages/components/timeline/src/timeline.ts"], "sourcesContent": ["import { defineComponent, h, provide, renderSlot } from 'vue'\nimport { useNamespace } from '@element-plus/hooks'\n\nconst Timeline = defineComponent({\n  name: 'ElTimeline',\n  setup(_, { slots }) {\n    const ns = useNamespace('timeline')\n\n    provide('timeline', slots)\n\n    /**\n     *  Maybe ,this component will not support prop 'reverse', why ?\n     *\n     *  Example 1:\n     *   <component-a>\n     *     <div>1</div>\n     *     <div>2</div>\n     *   </component-a>\n     *\n     *  Example 2:\n     *   <component-a>\n     *     <div v-for=\"i in 2\" :key=\"i\">{{ i }}</div>\n     *   </component-a>\n     *\n     *  'slots.default()' value in example 1 just like [Vnode, Vnode]\n     *  'slots.default()' value in example 2 just like [Vnode]\n     *\n     *   so i can't reverse the slots, when i use 'v-for' directive.\n     */\n\n    return () => {\n      return h('ul', { class: [ns.b()] }, [renderSlot(slots, 'default')])\n    }\n  },\n})\n\nexport default Timeline\nexport type TimelineInstance = InstanceType<typeof Timeline> & unknown\n"], "names": ["defineComponent", "useNamespace", "provide", "h", "renderSlot"], "mappings": ";;;;;;;AAEK,MAAC,QAAQ,GAAGA,mBAAe,CAAC;AACjC,EAAE,IAAI,EAAE,YAAY;AACpB,EAAE,KAAK,CAAC,CAAC,EAAE,EAAE,KAAK,EAAE,EAAE;AACtB,IAAI,MAAM,EAAE,GAAGC,kBAAY,CAAC,UAAU,CAAC,CAAC;AACxC,IAAIC,WAAO,CAAC,UAAU,EAAE,KAAK,CAAC,CAAC;AAC/B,IAAI,OAAO,MAAM;AACjB,MAAM,OAAOC,KAAC,CAAC,IAAI,EAAE,EAAE,KAAK,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAACC,cAAU,CAAC,KAAK,EAAE,SAAS,CAAC,CAAC,CAAC,CAAC;AAC1E,KAAK,CAAC;AACN,GAAG;AACH,CAAC;;;;"}