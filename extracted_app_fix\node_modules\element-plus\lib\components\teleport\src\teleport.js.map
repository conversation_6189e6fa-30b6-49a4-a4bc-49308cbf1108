{"version": 3, "file": "teleport.js", "sources": ["../../../../../../packages/components/teleport/src/teleport.ts"], "sourcesContent": ["import { buildProps, definePropType } from '@element-plus/utils'\nimport type { ExtractPropTypes } from 'vue'\nimport type Teleport from './teleport.vue'\n\nexport const teleportProps = buildProps({\n  to: {\n    type: definePropType<string | HTMLElement>([String, Object]),\n    required: true,\n  },\n  disabled: Boolean,\n} as const)\n\nexport type TeleportProps = ExtractPropTypes<typeof teleportProps>\nexport type TeleportInstance = InstanceType<typeof Teleport> & unknown\n"], "names": ["buildProps", "definePropType"], "mappings": ";;;;;;AACY,MAAC,aAAa,GAAGA,kBAAU,CAAC;AACxC,EAAE,EAAE,EAAE;AACN,IAAI,IAAI,EAAEC,sBAAc,CAAC,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;AAC1C,IAAI,QAAQ,EAAE,IAAI;AAClB,GAAG;AACH,EAAE,QAAQ,EAAE,OAAO;AACnB,CAAC;;;;"}