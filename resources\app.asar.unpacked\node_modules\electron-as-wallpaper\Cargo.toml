[package]
name = "electron-as-wallpaper"
edition = "2021"
include = ["src/**/*"]
publish = false

[lib]
crate-type = ["cdylib"]

[dependencies]
neon = "1"

[target."cfg(windows)".dependencies.windows]
version = "0.58.0"
features = [
  "Win32_UI_Input",
  "Win32_UI_WindowsAndMessaging",
  "Win32_System_LibraryLoader",
  "Win32_Foundation",
  "Win32_Graphics_Gdi",
  "Win32_Devices_HumanInterfaceDevice",
]
