{"version": 3, "file": "row.js", "sources": ["../../../../../../../packages/components/table-v2/src/components/row.tsx"], "sourcesContent": ["import {\n  computed,\n  defineComponent,\n  inject,\n  nextTick,\n  onMounted,\n  ref,\n  unref,\n} from 'vue'\nimport { isArray, isFunction, isNumber } from '@element-plus/utils'\nimport { tableV2RowProps } from '../row'\nimport { TableV2InjectionKey } from '../tokens'\nimport { placeholderSign } from '../private'\n\nimport type { CSSProperties, RendererElement, RendererNode, VNode } from 'vue'\nimport type { RowEventHandlers, TableV2RowProps } from '../row'\n\ntype CustomizedCellsType = VNode<\n  RendererNode,\n  RendererElement,\n  {\n    [key: string]: any\n  }\n>[]\n\ntype DefaultCellsType = VNode<\n  RendererNode,\n  RendererElement,\n  {\n    [key: string]: any\n  }\n>[][]\n\ntype ColumnCellsType = DefaultCellsType | CustomizedCellsType\n\nconst useTableRow = (props: TableV2RowProps) => {\n  const { isScrolling } = inject(TableV2InjectionKey)!\n\n  const measured = ref(false)\n  const rowRef = ref<HTMLElement>()\n  const measurable = computed(() => {\n    return isNumber(props.estimatedRowHeight) && props.rowIndex >= 0\n  })\n\n  const doMeasure = (isInit = false) => {\n    const $rowRef = unref(rowRef)\n    if (!$rowRef) return\n    const { columns, onRowHeightChange, rowKey, rowIndex, style } = props\n    const { height } = $rowRef.getBoundingClientRect()\n    measured.value = true\n\n    nextTick(() => {\n      if (isInit || height !== Number.parseInt(style!.height as string)) {\n        const firstColumn = columns[0]\n        const isPlaceholder = firstColumn?.placeholderSign === placeholderSign\n        onRowHeightChange?.(\n          { rowKey, height, rowIndex },\n          firstColumn && !isPlaceholder && firstColumn.fixed\n        )\n      }\n    })\n  }\n\n  const eventHandlers = computed(() => {\n    const { rowData, rowIndex, rowKey, onRowHover } = props\n    const handlers = props.rowEventHandlers || ({} as RowEventHandlers)\n    const eventHandlers = {} as {\n      [key in keyof RowEventHandlers]: (e: Event) => void\n    }\n\n    Object.entries(handlers).forEach(([eventName, handler]) => {\n      if (isFunction(handler)) {\n        eventHandlers[eventName as keyof RowEventHandlers] = (event: Event) => {\n          handler({\n            event,\n            rowData,\n            rowIndex,\n            rowKey,\n          })\n        }\n      }\n    })\n\n    if (onRowHover) {\n      ;(\n        [\n          { name: 'onMouseleave', hovered: false },\n          { name: 'onMouseenter', hovered: true },\n        ] as const\n      ).forEach(({ name, hovered }) => {\n        const existedHandler = eventHandlers[name]\n        eventHandlers[name] = ((event: MouseEvent) => {\n          onRowHover({\n            event,\n            hovered,\n            rowData,\n            rowIndex,\n            rowKey,\n          })\n\n          existedHandler?.(event)\n        }) as any\n      })\n    }\n    return eventHandlers\n  })\n\n  const onExpand = (expanded: boolean) => {\n    const { onRowExpand, rowData, rowIndex, rowKey } = props\n\n    onRowExpand?.({\n      expanded,\n      rowData,\n      rowIndex,\n      rowKey,\n    })\n  }\n\n  onMounted(() => {\n    if (unref(measurable)) {\n      doMeasure(true)\n    }\n  })\n\n  return { isScrolling, measurable, measured, rowRef, eventHandlers, onExpand }\n}\n\nconst COMPONENT_NAME = 'ElTableV2TableRow'\n\nconst TableV2Row = defineComponent({\n  name: COMPONENT_NAME,\n  props: tableV2RowProps,\n  setup(props, { expose, slots, attrs }) {\n    const {\n      eventHandlers,\n      isScrolling,\n      measurable,\n      measured,\n      rowRef,\n\n      onExpand,\n    } = useTableRow(props)\n\n    expose({\n      /**\n       * @description manually dispatching expand action on row.\n       */\n      onExpand,\n    })\n\n    return () => {\n      const {\n        columns,\n        columnsStyles,\n        expandColumnKey,\n        depth,\n        rowData,\n        rowIndex,\n        style,\n      } = props\n\n      let ColumnCells: ColumnCellsType = columns.map((column, columnIndex) => {\n        const expandable =\n          isArray(rowData.children) &&\n          rowData.children.length > 0 &&\n          column.key === expandColumnKey\n\n        return slots.cell!({\n          column,\n          columns,\n          columnIndex,\n          depth,\n          style: columnsStyles[column.key!],\n          rowData,\n          rowIndex,\n          isScrolling: unref(isScrolling),\n          expandIconProps: expandable\n            ? {\n                rowData,\n                rowIndex,\n                onExpand,\n              }\n            : undefined,\n        })\n      })\n\n      if (slots.row) {\n        ColumnCells = slots.row({\n          cells: ColumnCells.map((node) => {\n            if (isArray(node) && node.length === 1) {\n              return node[0]\n            }\n            return node\n          }),\n          style,\n          columns,\n          depth,\n          rowData,\n          rowIndex,\n          isScrolling: unref(isScrolling),\n        })\n      }\n\n      if (unref(measurable)) {\n        // eslint-disable-next-line @typescript-eslint/no-unused-vars\n        const { height, ...exceptHeightStyle } = style || {}\n        const _measured = unref(measured)\n        return (\n          <div\n            ref={rowRef}\n            class={props.class}\n            style={_measured ? style : exceptHeightStyle}\n            role=\"row\"\n            {...attrs}\n            {...unref(eventHandlers)}\n          >\n            {ColumnCells}\n          </div>\n        )\n      }\n\n      return (\n        <div\n          {...attrs}\n          ref={rowRef}\n          class={props.class}\n          style={style}\n          role=\"row\"\n          {...unref(eventHandlers)}\n        >\n          {ColumnCells}\n        </div>\n      )\n    }\n  },\n})\n\nexport default TableV2Row\n\nexport type TableV2RowCellRenderParam = {\n  column: TableV2RowProps['columns'][number]\n  columns: TableV2RowProps['columns']\n  columnIndex: number\n  depth: number\n  style: CSSProperties\n  rowData: any\n  rowIndex: number\n  isScrolling: boolean\n  expandIconProps?: {\n    rowData: any\n    rowIndex: number\n    onExpand: (expand: boolean) => void\n  }\n}\n"], "names": ["useTableRow", "isScrolling", "inject", "TableV2InjectionKey", "measured", "ref", "rowRef", "computed", "isNumber", "doMeasure", "columns", "onRowHeightChange", "<PERSON><PERSON><PERSON>", "rowIndex", "style", "height", "$rowRef", "getBoundingClientRect", "value", "nextTick", "parseInt", "firstColumn", "placeholderSign", "rowData", "onRowHover", "handlers", "props", "rowEventHandlers", "eventHandlers", "Object", "for<PERSON>ach", "eventName", "isFunction", "handler", "event", "name", "hovered", "onExpand", "onRowExpand", "unref", "onMounted", "defineComponent", "tableV2RowProps", "COMPONENT_NAME", "TableV2Row", "attrs", "measurable", "expose", "isArray", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "expandable", "slots", "depth", "column", "undefined", "cells", "node", "_createVNode", "_mergeProps"], "mappings": ";;;;;;;;;;;;AAmCA,EAAMA,MAAAA;IACE,WAAA;AAAEC,GAAAA,GAAAA,UAAAA,CAAAA,0BAAAA,CAAAA,CAAAA;EAAF,MAAkBC,QAAOC,GAAAA,OAAAA,CAAAA,KAAAA,CAAAA,CAAAA;AAE/B,EAAA,MAAMC,MAAQ,GAAAC,OAAM,EAAA,CAAC;EACrB,MAAMC,UAASD,GAAfE,YAAA,CAAA,MAAA;AACA,IAAA,OAAgBC,cAAA,CAAA,KAAW,CAAA,kBAAO,CAAA,IAAA,KAAA,CAAA,QAAA,IAAA,CAAA,CAAA;IAChC,CAAOA;AACR,EAAA,MAFD,SAAA,GAAA,CAAA,MAAA,GAAA,KAAA,KAAA;;AAIA,IAAA,IAAMC;AACJ,MAAA,OAAa;IACb;MACM,OAAA;MAAEC,iBAAF;MAAWC,MAAX;MAA8BC,QAA9B;MAAsCC,KAAtC;AAAgDC,KAAAA,GAAAA,KAAAA,CAAAA;AAAhD,IAAA,MAAN;MACM,MAAA;AAAEC,KAAAA,GAAAA,OAAAA,CAAAA,qBAAAA,EAAAA,CAAAA;IAAF,QAAaC,CAAAA,KAAQC,GAAAA,IAAAA,CAAAA;IAC3Bb,YAAQ,CAACc,MAAT;AAEAC,MAAAA,IAAAA,MAAe,IAAA,MAAA,KAAA,MAAA,CAAA,QAAA,CAAA,KAAA,CAAA,MAAA,CAAA,EAAA;AACb,QAAA,MAAU,WAAU,GAAA,OAAW,CAAA,CAAA,CAACC,CAAP;AACvB,QAAA,MAAMC,aAAW,GAAU,CAAA,WAA3B,IAAA,IAAA,GAAA,KAAA,CAAA,GAAA,WAAA,CAAA,eAAA,MAAAC,wBAAA,CAAA;AACA,QAAA,qBAAsBD,IAAAA,GAAAA,KAAW,CAAEC,GAAAA,kBAAoBA;AACvDX,UAAAA,MAAAA;UACIC,MAAF;UAAUG,QAAV;AAAkBF,SAAAA,EAAAA,WAAAA,IAAAA,CAAAA,aAAAA,IAAAA,WAAAA,CAAAA,KAAAA,CAAAA,CAAAA;;AAGrB,KAAA,CAAA,CAAA;AACF,GAAA,CAAA;EACF,MAjBD,aAAA,GAAAN,YAAA,CAAA,MAAA;;AAmBA,MAAA,OAAmB;MACX,QAAA;MAAEgB,MAAF;MAAWV,UAAX;QAAA,KAAA,CAAA;AAA6BW,IAAAA,MAAAA,QAAAA,GAAAA,KAAAA,CAAAA,gBAAAA,IAAAA,EAAAA,CAAAA;AAA7B,IAAA,MAAN,cAAA,GAAA,EAAA,CAAA;AACA,IAAA,MAAMC,QAAQ,CAAGC,QAAMC,CAAAA,CAAAA,OAAAA,CAAAA,CAAAA,CAAAA,SAAvB,EAAA,OAAA,CAAA,KAAA;MACMC,IAAAA,iBAAAA,CAAAA,OAAN,CAAA,EAAA;AAIAC,QAAAA,cAAeJ,CAAAA,SAAUK,CAAAA,GAAzB,CAAiC,KAAEC,KAAAA;AACjC,UAAIC,OAAU,CAAA;AACZJ,YAAAA,KAAAA;AACEK,YAAAA,OAAQ;YACNC,QADM;YAENX,MAFM;YAGNV,CAHM;AAIND,SAAAA,CAAAA;AAJM,OAAA;;AAOX,IAAA,IAAA,UAAA,EAAA;;AAGH,QAAIY,oBAAY;AACd,QAAA,OAAA,EAAA,KAAA;OAEI,EAAA;AAAEW,QAAAA,IAAI,EAAE,cAAR;AAAwBC,QAAAA,OAAO,EAAE,IAAA;AAAjC,OADF,CAEE,CAAA,OAAA,CAAA,CAAA;AAAED,QAAAA,IAAI;AAAkBC,QAAAA,OAAO;AAA/B,OAFF,KADD;QAKYD,MAAF,cAAA,GAAA,cAAA,CAAA,IAAA,CAAA,CAAA;AAAQC,QAAAA,cAAAA,CAAAA,IAAAA,CAAAA,GAAAA,CAAAA,KAAAA,KAAAA;AAAR,UAAsB,UAAA,CAAA;AAC/B,YAAA,KAAoB;;AACpBR,YAAAA,OAAAA;AACEJ,YAAAA,QAAU;YACRU,MADS;YAETE,CAFS;wBAAA,IAAA,IAAA,GAAA,KAAA,CAAA,GAAA,cAAA,CAAA,KAAA,CAAA,CAAA;;AAKTxB,OAAAA,CAAAA,CAAAA;AALS,KAAA;WAQG,cAAA,CAAA;;QAhBjB,QAAA,GAAA,CAAA,QAAA,KAAA;AAmBF,IAAA,MAAA;;AACD,MAAA,OAAA;AACD,MA1CD,QAAA;;KA4CMyB,GAAAA,KAAAA,CAAAA;IACJ,WAAM,IAAA,IAAA,GAAA,KAAA,CAAA,GAAA,WAAA,CAAA;MAAEC,QAAF;MAAef,OAAf;MAAwBV,QAAxB;AAAkCD,MAAAA,MAAAA;AAAlC,KAAA,CAAA,CAAA;AAEN0B,GAAAA,CAAAA;eAAc,CAAA,MAAA;QAAAC,SAAA,CAAA,UAAA,CAAA,EAAA;MAGZ1B,SAHY,CAAA,IAAA,CAAA,CAAA;AAIZD,KAAAA;AAJY,GAAA,CAAA,CAAH;EAMZ,OATD;;AAWA4B,IAAAA,UAAgB;AACd,IAAA,QAAS;UACE;AACV,IAAA,aAAA;AACF,IAJD,QAAA;GAMO,CAAA;;MAAA,cAAA,GAAA,mBAAA,CAAA;MAAA,UAAA,GAAAC,mBAAA,CAAA;MAAA,EAAA,cAAA;OAAA,EAAAC,mBAAA;AAA4DL,EAAAA,KAAAA,CAAAA,KAAAA,EAAAA;IAAnE,MAAA;AACD,IA1FD,KAAA;;AA4FA,GAAMM,EAAAA;AAEN,IAAMC,MAAAA;AACJT,MAAI,aAD6B;AAEjCT,MAAAA,WAFiC;;MAG5B,QAAQ;MAAA,MAAA;MAAA,QAAA;AAAiBmB,KAAAA,GAAAA,WAAAA,CAAAA,KAAAA,CAAAA,CAAAA;AAAjB,IAA0B,MAAA,CAAA;MAC/B,QAAA;MACJjB,CADI;WAAA,MAAA;MAGJkB,MAHI;QAAA,OAAA;QAAA,aAAA;AAOJT,QAAAA,eAAAA;QACErC,KAAAA;AAEJ+C,QAAAA,OAAO;AACL,QAAA,QAAA;AACN,QAAA,KAAA;AACA,OAAA,GAAA,KAAA,CAAA;AACMV,MAAAA,IAAAA,WAAAA,GAAAA,OAAAA,CAAAA,GAAAA,CAAAA,CAAAA,MAAAA,EAAAA,WAAAA,KAAAA;AAJK,QAAP,MAAA,UAAA,GAAAW,cAAA,CAAA,OAAA,CAAA,QAAA,CAAA,IAAA,OAAA,CAAA,QAAA,CAAA,MAAA,GAAA,CAAA,IAAA,MAAA,CAAA,GAAA,KAAA,eAAA,CAAA;AAOA,QAAA,OAAa,KAAA,CAAA,IAAA,CAAA;UACL,MAAA;UAAA,OAAA;UAAA,WAAA;UAAA,KAAA;UAAA,KAAA,EAAA,aAAA,CAAA,MAAA,CAAA,GAAA,CAAA;UAAA,OAAA;UAAA,QAAA;AAOJlC,UAAAA,WAAAA,EAAAA,SAAAA,CAAAA,WAAAA,CAAAA;AAPI,UAQFY,eARJ,EAAA,UAAA,GAAA;YAUIuB,OAAAA;YACIC,QAAAA;YAKCC,QAAK;WAAO,GAAA,KAAA,CAAA;UAEjBzC,CAFiB;;UAIjB0C,KAJiB,CAAA,GAAA,EAAA;AAKjBtC,QAAAA,WAAoB,GAAA,KAAA,CAAA,GAACuC,CAAM;UAC3B9B,KANiB,EAAA,WAAA,CAAA,GAAA,CAAA,CAAA,IAAA,KAAA;YAAA,IAAAyB,cAAA,CAAA,IAAA,CAAA,IAAA,IAAA,CAAA,MAAA,KAAA,CAAA,EAAA;AAQjB/C,cAAAA,OAAW,IAAEsC,CAAAA,CAAAA,CAAK,CAACtC;aACJ;YAETsB,OADF,IAAA,CAAA;YAEEV;AACAwB,UAAAA,KAAAA;AAHF,UAAA,OAKAiB;AAfa,UAAnB,KAAA;AAiBD,UAvBD,OAAA;;UAyBIH,WAAW,EAAAZ,SAAA,CAAA,WAAA,CAAA;AACbU,SAAAA,CAAAA,CAAAA;AACEM,OAAAA;mBACMP,CAAAA,UAAQQ,CAAAA,EAAR;cACF;AACD,UAAA,MAAA;;AACD,SAAA,GAAA,KAAA,IAAA,EAAA,CAAA;AACD,QAAA,MANqB,SAAA,GAAAjB,SAAA,CAAA,QAAA,CAAA,CAAA;eAAAkB,eAAA,CAAA,KAAA,EAAAC,cAAA,CAAA;UAQtBhD,KARsB,EAAA,MAAA;UAStB0C,OATsB,EAAA,KAAA,CAAA,KAAA;UAUtB7B,OAVsB,EAAA,SAAA,GAAA,KAAA,GAAA,iBAAA;UAWtBV,MAXsB,EAAA,KAAA;WAYX,KAAA,EAAA0B,SAAEA,CAAK,aAAA,CAAA,CAAA,EAAA,CAAA,WAAA,CAAA,CAAA,CAAA;AAZI,OAAA;AAczB,MAAA,OAAAkB,eAAA,CAAA,KAAA,EAAAC,cAAA,CAAA,KAAA,EAAA;;AAED,QAAA,OAAS,EAAA,KAAA,CAAA,KAAc;AACrB,QAAA,OAAA,EAAA,KAAA;QACA,MAAM,EAAA,KAAA;kBAAA,CAAA,aAAA,CAAA,CAAA,EAAA,CAAA,WAAA,CAAA,CAAA,CAAA;;;;AACN,UAAe,UAAA;;;;"}