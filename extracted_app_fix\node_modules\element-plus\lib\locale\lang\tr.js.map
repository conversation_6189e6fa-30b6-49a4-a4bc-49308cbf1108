{"version": 3, "file": "tr.js", "sources": ["../../../../../packages/locale/lang/tr.ts"], "sourcesContent": ["export default {\n  name: 'tr',\n  el: {\n    breadcrumb: {\n      label: 'Breadcrumb', // to be translated\n    },\n    colorpicker: {\n      confirm: '<PERSON><PERSON><PERSON>',\n      clear: '<PERSON><PERSON><PERSON>',\n    },\n    datepicker: {\n      now: '<PERSON><PERSON><PERSON>',\n      today: '<PERSON><PERSON><PERSON><PERSON>',\n      cancel: '<PERSON>pta<PERSON>',\n      clear: 'Temizle',\n      confirm: '<PERSON><PERSON><PERSON>',\n      selectDate: '<PERSON><PERSON><PERSON> seç',\n      selectTime: 'Saat seç',\n      startDate: 'Başlangıç Tarihi',\n      startTime: '<PERSON>şlangıç Saati',\n      endDate: 'Bitiş Tarihi',\n      endTime: '<PERSON><PERSON><PERSON> Saati',\n      prevYear: 'Önceki Yıl',\n      nextYear: '<PERSON><PERSON><PERSON> Yıl',\n      prevMonth: 'Önceki Ay',\n      nextMonth: '<PERSON>rak<PERSON> Ay',\n      year: '',\n      month1: 'Ocak',\n      month2: 'Şubat',\n      month3: 'Mart',\n      month4: 'Nisan',\n      month5: 'Mayıs',\n      month6: 'Haziran',\n      month7: 'Temmuz',\n      month8: 'Ağust<PERSON>',\n      month9: '<PERSON><PERSON><PERSON><PERSON>',\n      month10: '<PERSON><PERSON>',\n      month11: 'Kasım',\n      month12: 'Ara<PERSON><PERSON><PERSON>',\n      // week: 'week',\n      weeks: {\n        sun: 'Paz',\n        mon: 'Pzt',\n        tue: 'Sal',\n        wed: 'Çar',\n        thu: 'Per',\n        fri: 'Cum',\n        sat: 'Cmt',\n      },\n      months: {\n        jan: 'Oca',\n        feb: 'Şub',\n        mar: 'Mar',\n        apr: 'Nis',\n        may: 'May',\n        jun: 'Haz',\n        jul: 'Tem',\n        aug: 'Ağu',\n        sep: 'Eyl',\n        oct: 'Eki',\n        nov: 'Kas',\n        dec: 'Ara',\n      },\n    },\n    select: {\n      loading: 'Yükleniyor',\n      noMatch: 'Eşleşen veri bulunamadı',\n      noData: 'Veri yok',\n      placeholder: 'Seç',\n    },\n    mention: {\n      loading: 'Yükleniyor',\n    },\n    cascader: {\n      noMatch: 'Eşleşen veri bulunamadı',\n      loading: 'Yükleniyor',\n      placeholder: 'Seç',\n      noData: 'Veri yok',\n    },\n    pagination: {\n      goto: 'Git',\n      pagesize: '/sayfa',\n      total: 'Toplam {total}',\n      pageClassifier: '',\n      page: 'Page', // to be translated\n      prev: 'Go to previous page', // to be translated\n      next: 'Go to next page', // to be translated\n      currentPage: 'page {pager}', // to be translated\n      prevPages: 'Previous {pager} pages', // to be translated\n      nextPages: 'Next {pager} pages', // to be translated\n    },\n    messagebox: {\n      title: 'Mesaj',\n      confirm: 'Onayla',\n      cancel: 'İptal',\n      error: 'İllegal giriş',\n    },\n    upload: {\n      deleteTip: 'kaldırmak için delete tuşuna bas',\n      delete: 'Sil',\n      preview: 'Görüntüle',\n      continue: 'Devam',\n    },\n    table: {\n      emptyText: 'Veri yok',\n      confirmFilter: 'Onayla',\n      resetFilter: 'Sıfırla',\n      clearFilter: 'Hepsi',\n      sumText: 'Sum',\n    },\n    tree: {\n      emptyText: 'Veri yok',\n    },\n    transfer: {\n      noMatch: 'Eşleşen veri bulunamadı',\n      noData: 'Veri yok',\n      titles: ['Liste 1', 'Liste 2'],\n      filterPlaceholder: 'Anahtar kelimeleri gir',\n      noCheckedFormat: '{total} adet',\n      hasCheckedFormat: '{checked}/{total} seçildi',\n    },\n    image: {\n      error: 'BAŞARISIZ OLDU',\n    },\n    pageHeader: {\n      title: 'Geri',\n    },\n    popconfirm: {\n      confirmButtonText: 'Evet',\n      cancelButtonText: 'Hayır',\n    },\n    carousel: {\n      leftArrow: 'Carousel arrow left', // to be translated\n      rightArrow: 'Carousel arrow right', // to be translated\n      indicator: 'Carousel switch to index {index}', // to be translated\n    },\n  },\n}\n"], "names": [], "mappings": ";;;;AAAA,SAAe;AACf,EAAE,IAAI,EAAE,IAAI;AACZ,EAAE,EAAE,EAAE;AACN,IAAI,UAAU,EAAE;AAChB,MAAM,KAAK,EAAE,YAAY;AACzB,KAAK;AACL,IAAI,WAAW,EAAE;AACjB,MAAM,OAAO,EAAE,QAAQ;AACvB,MAAM,KAAK,EAAE,SAAS;AACtB,KAAK;AACL,IAAI,UAAU,EAAE;AAChB,MAAM,GAAG,EAAE,YAAY;AACvB,MAAM,KAAK,EAAE,UAAU;AACvB,MAAM,MAAM,EAAE,YAAY;AAC1B,MAAM,KAAK,EAAE,SAAS;AACtB,MAAM,OAAO,EAAE,QAAQ;AACvB,MAAM,UAAU,EAAE,cAAc;AAChC,MAAM,UAAU,EAAE,aAAa;AAC/B,MAAM,SAAS,EAAE,+BAA+B;AAChD,MAAM,SAAS,EAAE,8BAA8B;AAC/C,MAAM,OAAO,EAAE,mBAAmB;AAClC,MAAM,OAAO,EAAE,kBAAkB;AACjC,MAAM,QAAQ,EAAE,oBAAoB;AACpC,MAAM,QAAQ,EAAE,kBAAkB;AAClC,MAAM,SAAS,EAAE,cAAc;AAC/B,MAAM,SAAS,EAAE,YAAY;AAC7B,MAAM,IAAI,EAAE,EAAE;AACd,MAAM,MAAM,EAAE,MAAM;AACpB,MAAM,MAAM,EAAE,YAAY;AAC1B,MAAM,MAAM,EAAE,MAAM;AACpB,MAAM,MAAM,EAAE,OAAO;AACrB,MAAM,MAAM,EAAE,YAAY;AAC1B,MAAM,MAAM,EAAE,SAAS;AACvB,MAAM,MAAM,EAAE,QAAQ;AACtB,MAAM,MAAM,EAAE,cAAc;AAC5B,MAAM,MAAM,EAAE,UAAU;AACxB,MAAM,OAAO,EAAE,MAAM;AACrB,MAAM,OAAO,EAAE,YAAY;AAC3B,MAAM,OAAO,EAAE,aAAa;AAC5B,MAAM,KAAK,EAAE;AACb,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,QAAQ;AACrB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,OAAO;AACP,MAAM,MAAM,EAAE;AACd,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,UAAU;AACvB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,UAAU;AACvB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,OAAO;AACP,KAAK;AACL,IAAI,MAAM,EAAE;AACZ,MAAM,OAAO,EAAE,eAAe;AAC9B,MAAM,OAAO,EAAE,wCAAwC;AACvD,MAAM,MAAM,EAAE,UAAU;AACxB,MAAM,WAAW,EAAE,QAAQ;AAC3B,KAAK;AACL,IAAI,OAAO,EAAE;AACb,MAAM,OAAO,EAAE,eAAe;AAC9B,KAAK;AACL,IAAI,QAAQ,EAAE;AACd,MAAM,OAAO,EAAE,wCAAwC;AACvD,MAAM,OAAO,EAAE,eAAe;AAC9B,MAAM,WAAW,EAAE,QAAQ;AAC3B,MAAM,MAAM,EAAE,UAAU;AACxB,KAAK;AACL,IAAI,UAAU,EAAE;AAChB,MAAM,IAAI,EAAE,KAAK;AACjB,MAAM,QAAQ,EAAE,QAAQ;AACxB,MAAM,KAAK,EAAE,gBAAgB;AAC7B,MAAM,cAAc,EAAE,EAAE;AACxB,MAAM,IAAI,EAAE,MAAM;AAClB,MAAM,IAAI,EAAE,qBAAqB;AACjC,MAAM,IAAI,EAAE,iBAAiB;AAC7B,MAAM,WAAW,EAAE,cAAc;AACjC,MAAM,SAAS,EAAE,wBAAwB;AACzC,MAAM,SAAS,EAAE,oBAAoB;AACrC,KAAK;AACL,IAAI,UAAU,EAAE;AAChB,MAAM,KAAK,EAAE,OAAO;AACpB,MAAM,OAAO,EAAE,QAAQ;AACvB,MAAM,MAAM,EAAE,YAAY;AAC1B,MAAM,KAAK,EAAE,yBAAyB;AACtC,KAAK;AACL,IAAI,MAAM,EAAE;AACZ,MAAM,SAAS,EAAE,+CAA+C;AAChE,MAAM,MAAM,EAAE,KAAK;AACnB,MAAM,OAAO,EAAE,oBAAoB;AACnC,MAAM,QAAQ,EAAE,OAAO;AACvB,KAAK;AACL,IAAI,KAAK,EAAE;AACX,MAAM,SAAS,EAAE,UAAU;AAC3B,MAAM,aAAa,EAAE,QAAQ;AAC7B,MAAM,WAAW,EAAE,mBAAmB;AACtC,MAAM,WAAW,EAAE,OAAO;AAC1B,MAAM,OAAO,EAAE,KAAK;AACpB,KAAK;AACL,IAAI,IAAI,EAAE;AACV,MAAM,SAAS,EAAE,UAAU;AAC3B,KAAK;AACL,IAAI,QAAQ,EAAE;AACd,MAAM,OAAO,EAAE,wCAAwC;AACvD,MAAM,MAAM,EAAE,UAAU;AACxB,MAAM,MAAM,EAAE,CAAC,SAAS,EAAE,SAAS,CAAC;AACpC,MAAM,iBAAiB,EAAE,wBAAwB;AACjD,MAAM,eAAe,EAAE,cAAc;AACrC,MAAM,gBAAgB,EAAE,8BAA8B;AACtD,KAAK;AACL,IAAI,KAAK,EAAE;AACX,MAAM,KAAK,EAAE,qBAAqB;AAClC,KAAK;AACL,IAAI,UAAU,EAAE;AAChB,MAAM,KAAK,EAAE,MAAM;AACnB,KAAK;AACL,IAAI,UAAU,EAAE;AAChB,MAAM,iBAAiB,EAAE,MAAM;AAC/B,MAAM,gBAAgB,EAAE,YAAY;AACpC,KAAK;AACL,IAAI,QAAQ,EAAE;AACd,MAAM,SAAS,EAAE,qBAAqB;AACtC,MAAM,UAAU,EAAE,sBAAsB;AACxC,MAAM,SAAS,EAAE,kCAAkC;AACnD,KAAK;AACL,GAAG;AACH,CAAC;;;;"}